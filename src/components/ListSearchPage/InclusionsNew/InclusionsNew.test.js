import React from 'react';
import { mountUtils } from 'test-utils';
import InclusionsNew from './InclusionsNew';
import { useFeature } from '@optimizely/react-sdk';
import {
  defaultInclusions,
  twoInclusions,
  threeInclusions,
  fourInclusions,
  whiteListOrderIconNames,
  whiteListOrderTitles,
  receivedOrderIconNames,
  receivedOrderTitles,
} from '../fixtures';

jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const decorators = { theme: true };
const render = (inclusions = defaultInclusions) => mountUtils(<InclusionsNew inclusions={inclusions} />, { decorators });

describe('The Inclusions', () => {
  const runInclusionTests = (useGlobalInclusionsValue) => {
    beforeEach(() => {
      useFeature.mockReturnValue([useGlobalInclusionsValue]);
    });

    describe('when there are more than 3 inclusions', () => {
      it('renders a maximum of 3 icons', () => {
        const { find } = render(defaultInclusions);
        expect(find('Icon')).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          const { findByTestId } = render(defaultInclusions);
          const expectedTitles = useGlobalInclusionsValue ? receivedOrderTitles : whiteListOrderTitles;
          findByTestId('inclusion-new').forEach((node, index) => {
            expect(node).toHaveProp({ title: expectedTitles[index] });
          });
        });
      });

      describe('The Icons', () => {
        it('renders the icons in the correct order', () => {
          const { find } = render(defaultInclusions);
          const expectedIconNames = useGlobalInclusionsValue ? receivedOrderIconNames : whiteListOrderIconNames;
          find('Icon').forEach((node, index) => {
            expect(node).toHaveProp({ name: expectedIconNames[index] });
          });
        });
      });

      describe('The "+ N more inclusions" text', () => {
        it('is displayed when there are more than 3 inclusions', () => {
          const { findByText } = render(defaultInclusions);
          expect(findByText('+ 2 more inclusions')).toBeDefined();
        });

        it('displays correct inclusion spelling if there is only 1 more inclusion', () => {
          const { findByText } = render(fourInclusions);
          expect(findByText('+ 1 more inclusion')).toBeDefined();
        });

        it('is not displayed when there are 3 or fewer inclusions', () => {
          const noMoreInclusions = render(threeInclusions);
          expect(noMoreInclusions.find('Text').someWhere((n) => n.text() === '+ 0 more inclusions')).toBe(false);

          const negativeInclusions = render(twoInclusions);
          expect(negativeInclusions.find('Text').someWhere((n) => n.text() === '+ -1 more inclusions')).toBe(false);
        });
      });
    });
  };

  describe('when useGlobalInclusions is false', () => {
    runInclusionTests(false);
  });

  describe('when useGlobalInclusions is true', () => {
    runInclusionTests(true);
  });
});
