import React from 'react';
import { Decimal } from 'decimal.js';
import { mountUtils, mocked } from 'test-utils';
import PriceDetails from './PriceDetails';
import { getPayableLaterDueDate } from 'store/quote/quoteSelectors';
import {
  getPointsAmount,
  getVoucherAmount,
  getPayableNowCashAmount,
  getPayableLaterCashAmount,
  getTravelPassAmount,
  getInitialCashAmount,
  getPayWith,
} from 'store/checkout/checkoutSelectors';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/quote/quoteSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough');

mountUtils.mockComponent('PaymentBreakdown');
mountUtils.mockComponent('PriceBreakdownModal');

const payableAtPropertyTotal = {
  amount: new Decimal(10),
  currency: 'AUD',
};

const property = {
  name: 'Test hotel',
};

const offer = {
  type: 'standard',
  charges: {
    payableAtProperty: { total: payableAtPropertyTotal },
    payableAtBooking: { total: { pointsStrikethrough: 2000, points: 1000 } },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
    },
  },
};

const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);
const pointsAmount = new Decimal(1000);
const voucherAmount = new Decimal(40);
const travelPassAmount = new Decimal(100);
const payableNowCashAmount = new Decimal(100);
const payableLaterCashAmount = new Decimal(200);
const payableLaterDueDate = new Date(2020, 10, 10);
const initialCashAmount = new Decimal(0);

const baseProps = {
  property,
  offer,
  checkIn,
  checkOut,
};

const render = (props) =>
  mountUtils(<PriceDetails {...baseProps} {...props} />, { decorators: { theme: true, store: true, router: true } });

beforeEach(() => {
  getPointsAmount.mockReturnValue(pointsAmount);
  getVoucherAmount.mockReturnValue(voucherAmount);
  getTravelPassAmount.mockReturnValue(travelPassAmount);
  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getPayableLaterCashAmount.mockReturnValue(payableLaterCashAmount);
  getPayableLaterDueDate.mockReturnValue(payableLaterDueDate);
  getInitialCashAmount.mockReturnValue(initialCashAmount);
  getPayWith.mockReturnValue('cash');
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    isPriceStrikethroughEnabled: false,
  });
});

it('renders the PaymentBreakdown', () => {
  const { find } = render();
  expect(find('PaymentBreakdown')).toHaveProp({
    payableNowCashAmount,
    payableLaterCashAmount,
    payableLaterDueDate,
    pointsAmount,
    travelPassAmount,
    payableAtProperty: payableAtPropertyTotal,
    priceStrikethrough: baseProps.offer.charges.strikethrough.price,
  });
});

it('renders the PriceBreakdownModal', () => {
  const { find } = render();
  expect(find('PriceBreakdownModal')).toHaveProp({
    property,
    offer,
    checkIn,
    checkOut,
  });
});

it('renders the voucher details', () => {
  const { find } = render();
  expect(find('Currency[data-testid="voucher-amount"]')).toHaveProp({
    amount: voucherAmount.negated(),
    currency: 'AUD',
  });
});

describe('without a voucher', () => {
  it('does not render the voucher details', () => {
    getVoucherAmount.mockReturnValue(new Decimal(0));
    const { findByTestId } = render();
    expect(findByTestId('voucher-amount')).not.toExist();
  });
});

describe('when in PPP mode', () => {
  beforeEach(() => {
    getPayWith.mockReturnValue('points');
    getInitialCashAmount.mockReturnValue(new Decimal(1000));
  });

  describe('and qantas-hotels-price-strikethrough feature flag OFF', () => {
    it('does NOT render the PriceStrikethrough', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });

    it('does NOT render PriceBreakdownModal', () => {
      const { findByTestId } = render();

      expect(findByTestId('price-breakdown-modal-ppp')).not.toExist();
      expect(findByTestId('price-breakdown-modal')).not.toExist();
    });
  });

  describe('and qantas-hotels-price-strikethrough feature flag ON', () => {
    beforeEach(() => {
      mocked(usePriceStrikethrough).mockReturnValue({
        isReady: true,
        isPriceStrikethroughEnabled: true,
      });
    });
    it('send the correct props to Currency and PriceStrikethrough', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).toHaveProp({
        price: { amount: offer.charges.payableAtBooking.total.pointsStrikethrough, currency: 'PTS' },
      });

      expect(find('Currency').first()).toHaveProp({
        amount: offer.charges.payableAtBooking.total.points,
      });
    });
  });
});
