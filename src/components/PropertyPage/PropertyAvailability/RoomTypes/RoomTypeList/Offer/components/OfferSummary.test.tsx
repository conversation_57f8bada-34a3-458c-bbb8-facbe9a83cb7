import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import OfferSummary from './OfferSummary';
import { useFeature } from '@optimizely/react-sdk';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { VALUE_ADD_SASH } from 'config/constants';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';

jest.mock('hooks/useDataLayer');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('./../../../../../../../components/Modal', () => {
  const Mock = () => <div />;
  return Mock;
});
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/optimizely/usePriceStrikethrough');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));
jest.mock('hooks/optimizely/useAvailableRoomsMessage');

mountUtils.mockComponent('PriceStrikethrough');
const offer = {
  type: 'standard',
  description: 'Junior Suite',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non-refundable unless you are entitled to a refund or other remedy under the Australian Consumer Law.',
    cancellationWindows: [],
  },
  name: 'Breakfast Included - Non-refundable',
  charges: {
    total: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '297.99',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: 0,
      currency: 'AUD',
    },
    payableAtProperty: {
      total: {
        amount: 0,
        currency: 'AUD',
      },
    },
    totalCash: { amount: '297.99', currency: 'AUD' },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
    },
  },
  inclusions: [
    {
      code: 'breakfast',
      name: 'Breakfast Included',
      description: 'Breakfast Buffet',
      icon: 'incQfBreakfast',
    },
  ],
  valueAdds: [],
  id: '202017398_230005304_493003',
  promotion: null,
  depositPay: {
    depositPayable: true,
  },
  pointsEarned: {
    maxQffEarnPpd: 1,
    qbrPoints: { total: 1 },
    qffPoints: { qffPointsClub: 10, total: 10, bonus: 0, base: 0 },
    promotionMultiplier: 1,
    propertyPpd: 1,
  },
};

const promotion = { name: 'a great deal' };
const valueAdds = ['Free bottle of wine'];
const checkIn = new Date(2019, 3, 30);
const checkOut = new Date(2019, 4, 1);
const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();

const defaultProps = {
  offer: offer,
  toggleOfferExpanded,
};

const render = (props) => mountUtils(<OfferSummary {...defaultProps} {...props} />, { decorators: { store: true } });

beforeEach(() => {
  jest.resetAllMocks();
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(getQueryCheckIn).mockReturnValue(checkIn);
  mocked(getQueryCheckOut).mockReturnValue(checkOut);
  mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => false });
  mocked(useFeature).mockReturnValue([false]);
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    isPriceStrikethroughEnabled: false,
  });
  mocked(useAvailableRoomsMessage).mockReturnValue({
    isReady: true,
    isAvailableRoomsMessageEnabled: false,
    max_rooms_cutoff: 5,
  });
});

it('renders the offer name', () => {
  const { find } = render({});
  expect(find('Heading')).toHaveText(offer.name);
});

it('renders Currency', () => {
  const { find } = render({});
  expect(find('Currency')).toHaveProp({
    amount: offer.charges.total.amount,
    currency: offer.charges.total.currency,
  });
});

describe('when in cash mode', () => {
  it('does not render the * after the currency', () => {
    const { findByTestId } = render({});
    expect(findByTestId('asterisk')).not.toExist();
  });
});

describe('when useGlobalInclusions is true', () => {
  beforeEach(() => {
    mocked(useFeature).mockReturnValue([true]);
  });

  describe('with no ValueAdds', () => {
    it('renders the inclusions', () => {
      const { find } = render({});
      expect(find('Flag').first()).toHaveLength(1);
      expect(find('Flag').at(0)).toHaveProp({ icon: 'incQfBreakfast' });
    });
  });

  describe('with ValueAdds', () => {
    it('renders the inclusions and ValueAdds with this first', () => {
      const { findByTestId } = render({
        offer: {
          ...offer,
          valueAdds,
        },
      });
      expect(findByTestId('inclusions-preview-wrapper-desktop').find('Flag')).toHaveLength(2);
      expect(findByTestId('inclusions-preview-wrapper-desktop').find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
    });

    describe('and on Mobile views', () => {
      beforeEach(() => {
        mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      });

      it('renders only one ValueAdds icon', () => {
        const { findByTestId } = render({
          offer: {
            ...offer,
            valueAdds: ['Free dinner for two', 'Free bottle of wine', 'Room upgrade'],
          },
        });

        expect(findByTestId('inclusions-preview-wrapper-mobile').find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
        expect(findByTestId('inclusions-preview-wrapper-mobile').find('Flag').at(1)).not.toHaveProp({ icon: 'welcomeGift' });
      });
    });
  });
});

describe('when useGlobalInclusions is false', () => {
  beforeEach(() => {
    mocked(useFeature).mockReturnValue([false]);
  });

  describe('with no ValueAdds', () => {
    it('renders the inclusions', () => {
      const { find } = render({});
      expect(find('Flag').first()).toHaveLength(1);
      expect(find('Flag').at(0)).toHaveProp({ icon: 'freeBreakfast' });
    });
  });

  describe('with ValueAdds', () => {
    it('renders the inclusions and ValueAdds with this first', () => {
      const { findByTestId } = render({
        offer: {
          ...offer,
          valueAdds,
        },
      });

      expect(findByTestId('inclusions-preview-wrapper-desktop').find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
      expect(findByTestId('inclusions-preview-wrapper-desktop').find('Flag')).toHaveLength(2);
    });

    describe('and on Mobile views', () => {
      beforeEach(() => {
        mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true });
      });

      it('renders only one ValueAdds icon', () => {
        const { findByTestId } = render({
          offer: {
            ...offer,
            valueAdds: ['Free dinner for two', 'Free bottle of wine', 'Room upgrade'],
          },
        });

        expect(findByTestId('inclusions-preview-wrapper-mobile').find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
        expect(findByTestId('inclusions-preview-wrapper-mobile').find('Flag').at(1)).not.toHaveProp({ icon: 'welcomeGift' });
      });
    });
  });
});

it('renders the campaign message', () => {
  const { find } = render({});
  expect(find('CampaignPriceMessage')).toHaveProp({
    offerType: offer.type,
    currency: offer.charges.total.currency,
  });
});

it('fires the toggleOfferExpanded prop when the CTA is actioned', () => {
  const { findByTestId } = render({});
  findByTestId('expand-offer-summary').simulate('click');
  expect(toggleOfferExpanded).toHaveBeenCalledWith(true);
});

describe('discounts', () => {
  it('does not show a discount when there is no discount', () => {
    const { find } = render({});
    expect(find('PriceBeforeDiscount')).not.toExist();
  });

  describe('and it is NOT in POINTSPAY mode', () => {
    beforeEach(() => {
      mocked(getIsPointsPay).mockReturnValue(false);
    });

    it('renders the CampaignPriceMessage', () => {
      const { find } = render({});
      expect(find('CampaignPriceMessage')).toExist();
    });
  });
});

describe('PromotionalSash', () => {
  it('does NOT render when there is NO promotion and NO ValueAdds', () => {
    const { find } = render({
      offer: {
        ...offer,
        valueAdds: [],
        promotion: {},
      },
    });

    expect(find('PromotionalSash')).not.toExist();
  });

  describe('when there is a promotion', () => {
    describe('and the Value Adds', () => {
      it('sends the promotion name', () => {
        const { find } = render({
          offer: {
            ...offer,
            valueAdds,
            promotion,
          },
        });

        expect(find('PromotionalSash')).toHaveProp({ promotionName: promotion.name });
      });
    });

    describe('and NO the Value Adds', () => {
      it('sends the promotion name', () => {
        const { find } = render({
          offer: {
            ...offer,
            promotion: { name: promotion.name },
          },
        });

        expect(find('PromotionalSash')).toHaveProp({ promotionName: promotion.name });
      });
    });
  });

  describe('when there is NO promotion', () => {
    describe('and the Value Adds', () => {
      it('sends VALUE_ADD_SASH as promotion name', () => {
        const { find } = render({
          offer: {
            ...offer,
            valueAdds,
          },
        });

        expect(find('PromotionalSash')).toHaveProp({ promotionName: VALUE_ADD_SASH });
      });
    });
  });
});

it('sends an event to the dataLayer', () => {
  const { findByTestId } = render({});
  findByTestId('expand-offer-summary').simulate('click');

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Offer Details',
    value: 'Offer Expanded',
  });
});

describe('it renders the cancellation message', () => {
  describe('when NOT mobile', () => {
    it('renders the CancellationTooltip', () => {
      const { find } = render({});
      expect(find('CancellationTooltip')).toExist();
    });
  });

  describe('when mobile', () => {
    beforeEach(() => mocked(useBreakpoints).mockReturnValue({ isLessThanBreakpoint: () => true }));

    it('renders the CancellationRefundModal', () => {
      const { find } = render({});
      expect(find('CancellationRefundModal')).toExist();
    });
  });
});

describe('expand more', () => {
  it('it renders the button', () => {
    const { findByTestId } = render({});
    expect(findByTestId('expand-offer-summary')).toExist();
  });
});

describe('qantas-hotels-available-rooms-message experiment', () => {
  it('does not display message when experiment is OFF', () => {
    const { findByText, reRender } = render({ ...defaultProps, offer: { ...defaultProps.offer, allocationsAvailable: 1 } });

    expect(findByText('Hurry, we only have 1 left!')).not.toExist();

    mocked(useFeature).mockReturnValue([false]);

    reRender();

    expect(findByText('Hurry, we only have 1 room left!')).not.toExist();
  });

  it('does not display message when allocations prop is missing', () => {
    const { findByText } = render({ ...defaultProps });

    expect(findByText('Hurry, we only have 1 room left!')).not.toExist();
  });

  describe('when the Feature Flag is ON', () => {
    beforeEach(() => {
      mocked(useAvailableRoomsMessage).mockReturnValue({
        isReady: true,
        isAvailableRoomsMessageEnabled: true,
        max_rooms_cutoff: 5,
      });
    });

    it('does not display message when allocations is larger than max', () => {
      const { findByText } = render({ ...defaultProps, offer: { ...defaultProps.offer, allocationsAvailable: 6 } });

      expect(findByText('We only have 6 left')).not.toExist();
    });

    it('displays message', () => {
      const { findByText } = render({ ...defaultProps, offer: { ...defaultProps.offer, allocationsAvailable: 1 } });

      expect(findByText('Hurry, we only have 1 room left!')).toExist();
    });
  });
});

describe('PriceStrikethrough', () => {
  describe('when the qantas-hotels-price-strikethrough flag is OFF', () => {
    it('does not render the PriceStrikethrough', () => {
      const { find } = render(offer);

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });

  describe('when the qantas-hotels-price-strikethrough flag is ON', () => {
    beforeEach(() => {
      mocked(usePriceStrikethrough).mockReturnValue({
        isReady: true,
        isPriceStrikethroughEnabled: true,
      });
    });

    it('renders PriceStrikethrough with the correct props', () => {
      const { find } = render(offer);

      expect(find('PriceStrikethrough')).toHaveProp({
        price: offer.charges.strikethrough.price,
      });
    });

    it('does not render PriceStrikethrough when offer type is classic', () => {
      const { find } = render({
        offer: {
          ...offer,
          promotion: {
            name: 'Classic Reward',
          },
        },
      });

      expect(find('PriceStrikethrough')).not.toExist();
    });

    it('does not render PriceStrikethrough when price strikethrough is not available', () => {
      const { find } = render({
        offer: {
          ...offer,
          charges: {
            ...offer.charges,
            strikethrough: null,
          },
        },
      });

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });
});
