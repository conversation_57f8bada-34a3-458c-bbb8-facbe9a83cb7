import React from 'react';
import { render, screen } from '@testing-library/react';
import PriceStrikethrough, { PriceProps } from './PriceStrikethrough';
import { PAYMENT_CURRENCIES } from 'config/constants';

describe('PriceStrikethrough', () => {
  const cashPrice = { currency: PAYMENT_CURRENCIES.CASH, amount: '100000' };
  const ptsPrice = { currency: PAYMENT_CURRENCIES.POINTS, amount: '500000' };

  const renderComponent = (props: PriceProps) =>
    render(<PriceStrikethrough price={props.price} withDecimal={props.withDecimal} asSup={props.asSup} />);

  describe('when given a cash price', () => {
    it('renders the price strikethrough with cash symbol and formatted amount', () => {
      renderComponent({ price: cashPrice });
      const priceElement = screen.getByText('$100,000').closest('span');

      expect(priceElement).toBeInTheDocument();
      expect(priceElement).toHaveTextContent('$100,000');
      expect(priceElement).toHaveStyle('text-decoration: line-through');
    });

    it('renders the price with correct decimal places when withDecimal is true', () => {
      renderComponent({ price: { currency: PAYMENT_CURRENCIES.CASH, amount: '200.50' }, withDecimal: true });
      const priceElement = screen.getByText('$200.50');

      expect(priceElement).toBeInTheDocument();
      expect(priceElement).toHaveTextContent('$200.50');
    });

    it('does not render PTS symbol', () => {
      renderComponent({ price: cashPrice });
      expect(screen.queryByText(PAYMENT_CURRENCIES.POINTS)).not.toBeInTheDocument();
    });
  });

  describe('when given a points price', () => {
    it('renders the price strikethrough with PTS symbol and formatted amount', () => {
      renderComponent({ price: ptsPrice });
      const priceElement = screen.getByText(`500,000 ${PAYMENT_CURRENCIES.POINTS}`).closest('span');

      expect(priceElement).toBeInTheDocument();
      expect(priceElement).toHaveTextContent(`500,000 ${PAYMENT_CURRENCIES.POINTS}`);
      expect(priceElement).toHaveStyle('text-decoration: line-through');
    });

    it('does not render cash symbol', () => {
      renderComponent({ price: ptsPrice });
      expect(screen.queryByText('$')).not.toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('returns nothing when price is null or undefined', () => {
      const { container } = renderComponent({ price: undefined });
      expect(container).toBeEmptyDOMElement();
    });

    it('returns nothing when amount is null', () => {
      const { container } = renderComponent({ price: { currency: 'AUD', amount: null } });
      expect(container).toBeEmptyDOMElement();
    });

    it('returns nothing when amount is zero', () => {
      const { container } = renderComponent({ price: { currency: 'AUD', amount: '0' } });
      expect(container).toBeEmptyDOMElement();
    });

    it('handles invalid amount gracefully', () => {
      // Mock console.warn to prevent test output noise
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

      const { container } = renderComponent({ price: { currency: 'AUD', amount: 'invalid' } });
      expect(container).toBeEmptyDOMElement();
      expect(consoleSpy).toHaveBeenCalledWith(
        'PriceStrikethrough: Invalid price amount',
        expect.objectContaining({
          amount: 'invalid',
          currency: 'AUD',
        }),
      );

      consoleSpy.mockRestore();
    });
  });

  describe('accessibility', () => {
    it('includes screen reader text for cash prices', () => {
      const { container } = renderComponent({ price: cashPrice });
      expect(container.innerHTML).toContain('Original price was $100,000');
    });

    it('includes screen reader text for points prices', () => {
      const { container } = renderComponent({ price: ptsPrice });
      expect(container.innerHTML).toContain('Original price was 500,000 PTS');
    });

    it('screen reader text matches visual format', () => {
      const { container } = renderComponent({ price: ptsPrice });
      // Check that the screen reader text matches the visual format with commas and PTS
      expect(container.innerHTML).toContain('Original price was 500,000 PTS');
      expect(container.innerHTML).not.toContain('Original price was $500000');
    });
  });
});
