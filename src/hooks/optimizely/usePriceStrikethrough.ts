import { useDecision } from '@optimizely/react-sdk';

type Result = {
  isReady: boolean;
  isPriceStrikethroughEnabled: boolean;
};

const usePriceStrikethrough = (): Result => {
  const [decision, isReady] = useDecision('qantas-hotels-price-strikethrough', {
    autoUpdate: true,
  });

  return {
    isReady,
    isPriceStrikethroughEnabled: decision.enabled,
  };
};

export default usePriceStrikethrough;
