import React from 'react';
import { mocked, mountUtils } from 'test-utils';
import HomePageSearchPanel from './HomePageSearchPanel';
import { useDataLayer } from 'hooks/useDataLayer';
import { getHomePageBanner } from 'store/homePage/homePageSelectors';
import { useRouter } from 'next/router';
import { getIsMobileApp } from 'store/ui/uiSelectors';
import { ACTIVITIES_API_URL } from 'config';
import { useFeature } from '@optimizely/react-sdk';
import useTabClickEvent from 'hooks/useTabClickEvent';

jest.mock('store/homePage/homePageSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('next/router', () => ({ useRouter: jest.fn() }));
jest.mock('store/ui/uiSelectors');
window.open = jest.fn();
jest.useFakeTimers();
jest.mock('hooks/useTabClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    tabClickEvent: jest.fn(),
  })),
}));
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('HotelsTab');
mountUtils.mockComponent('AirbnbTab');
mountUtils.mockComponent('ActivitiesTab');

const mockBanner = {
  asset: {
    _ref: 'image-4bf004ffa6b38474026debfb71a8ef64571b11b7-1000x588-jpg',
    _type: 'reference',
  },
  caption: 'A picture of a crystal blue pool, surrounded by recliner chairs',
};

const mockRouter = {
  push: jest.fn(),
};

const emitInteractionEvent = jest.fn();
const mockTabClickEvent = jest.fn();

const decorators = { store: true, theme: true };
const render = () => mountUtils(<HomePageSearchPanel />, { decorators });

describe('<HomePageSearchPanel />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
    mocked(getHomePageBanner).mockReturnValue(mockBanner);
    mocked(useRouter).mockReturnValue(mockRouter);
    mocked(getIsMobileApp).mockReturnValue(false);
    mocked(useFeature).mockReturnValue([false]);
    mocked(useTabClickEvent).mockReturnValue({ tabClickEvent: mockTabClickEvent });
  });

  it('renders the background image', () => {
    const { find } = render();
    expect(find('HeroImage')).toHaveProp({
      src: 'https://cdn.sanity.io/images/abcdefghi/qh-test/4bf004ffa6b38474026debfb71a8ef64571b11b7-1000x588.jpg?w=2560&q=80&auto=format',
    });
  });

  it('renders the background image with the correct alt test', () => {
    const { find } = render();
    expect(find('HeroImage')).toHaveProp({
      alt: 'Book hotels and accommodation in Australia',
    });
  });

  it('renders the default background image if not banner is present in redux', () => {
    mocked(getHomePageBanner).mockReturnValue(null);
    const { find } = render();
    expect(find('HeroImage')).toHaveProp({
      src: '/hotels/images/placeholder-desktop-image.jpg',
    });
  });

  describe('when the activities feature is disabled', () => {
    it('renders the base tab list without the activities tab', () => {
      const { findByTestId } = render();
      expect(findByTestId('hotels-tab')).toHaveText('Hotels');
      expect(findByTestId('airbnb-tab')).toHaveText('Airbnb');
      expect(findByTestId('activities-tab')).not.toExist();
    });

    it('renders the hotels tab', () => {
      const { find } = render();
      expect(find('HotelsTab')).toExist();
    });

    it('renders the airbnb tab', () => {
      const { find } = render();
      expect(find('AirbnbTab')).toExist();
    });

    it('tracks when the hotels tab is clicked', () => {
      const { findByTestId } = render();
      findByTestId('hotels-tab').last().simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Search', value: 'Hotels Selected' });
      expect(mockTabClickEvent).toHaveBeenCalledWith({
        itemText: 'Hotels',
        itemName: 'Hotels search tab',
        pageType: 'homepage',
        tabIndex: 0,
        variant: 'homepage search tab',
      });
    });

    it('tracks when the airbnb tab is clicked', () => {
      const { findByTestId } = render();
      findByTestId('airbnb-tab').last().simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Search', value: 'Hotels Airbnb Selected' });
      expect(mockTabClickEvent).toHaveBeenCalledWith({
        itemText: 'Airbnb',
        itemName: 'Hotels Airbnb search tab',
        pageType: 'homepage',
        tabIndex: 1,
        variant: 'homepage search tab',
      });
    });
  });

  describe('when the activities feature is enabled', () => {
    beforeEach(() => {
      mocked(useFeature).mockReturnValue([true]);
    });

    it('renders the complete tab list', () => {
      const { findByTestId } = render();
      expect(findByTestId('hotels-tab')).toHaveText('Hotels');
      expect(findByTestId('airbnb-tab')).toHaveText('Airbnb');
      expect(findByTestId('activities-tab')).toHaveText('Activities');
    });

    it('tracks when the activities tab is clicked', () => {
      const { findByTestId } = render();
      findByTestId('activities-tab').last().simulate('click');
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Search', value: 'Hotels Activities Selected' });
      expect(mockTabClickEvent).toHaveBeenCalledWith({
        itemText: 'Activities',
        itemName: 'Hotels Activities search tab',
        pageType: 'homepage',
        tabIndex: 2,
        variant: 'homepage search tab',
      });
    });

    it('opens the activities page', () => {
      const { findByTestId } = render();

      findByTestId('activities-tab').last().simulate('mousedown');
      jest.advanceTimersByTime(500);

      expect(window.open).toHaveBeenCalledWith(ACTIVITIES_API_URL, '_blank');
    });
  });
});
