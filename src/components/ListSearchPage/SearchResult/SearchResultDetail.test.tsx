import React from 'react';
import PromotionalSash from 'components/PromotionalSash';
import Currency from 'components/Currency';
import { mocked, mountUtils } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';
import SearchResultDetail from './SearchResultDetail';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getCampaignDefaultSash } from 'store/campaign/campaignSelectors';
import { useShowFullPointsToggle } from './hooks/useShowFullPointsToggle';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';

jest.mock('config', () => ({
  SEARCH_DATE_FORMAT: 'yyyy-MM-dd',
  POINTS_EARN_ENABLED: true,
}));

jest.mock('hooks/useRatingTooltip');
jest.mock('store/router/routerSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough');
jest.mock('./hooks/useShowFullPointsToggle');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('SearchImageGallery');
mountUtils.mockComponent('PromotionalSash');
mountUtils.mockComponent('TripAdvisorRating');
mountUtils.mockComponent('InclusionsNew');
mountUtils.mockComponent('PointsEarnSummary');
mountUtils.mockComponent('CancellationRefundSummary');
mountUtils.mockComponent('PointsPerDollar');
mountUtils.mockComponent('PriceStrikethrough');

let result;
const checkIn = new Date(2020, 10, 10);
const checkOut = new Date(2020, 10, 11);
const cardIndex = 19;

const classicOfferName = 'Classic Reward';
const triplePointOfferName = 'Triple Points';
const genericOfferName = 'Test Promotion';

beforeEach(() => {
  jest.clearAllMocks();
  mocked(getQueryCheckIn).mockReturnValue(checkIn);
  mocked(getQueryCheckOut).mockReturnValue(checkOut);
  mocked(getCampaignDefaultSash).mockReturnValue(null);
  mocked(useFeature).mockReturnValue([false]);
  mocked(useShowFullPointsToggle).mockReturnValue({ showFullPoints: false, isReady: true });
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    isPriceStrikethroughEnabled: false,
  });
  mocked(useAvailableRoomsMessage).mockReturnValue({
    isReady: true,
    isAvailableRoomsMessageEnabled: false,
    max_rooms_cutoff: 5,
  });

  result = {
    offer: {
      allocationsAvailable: 4,
      charges: {
        total: {
          amount: '200',
          currency: 'AUD',
        },
        strikethrough: {
          price: { currency: 'PTS', amount: '10000' },
        },
      },
      cancellationPolicy: {
        isNonrefundable: true,
        description: 'sorry no refunds',
        cancellationWindows: [
          {
            currency: 'AUD',
            endTime: '2020-05-09T14:00:00+10:00',
            formattedBeforeDate: 'Thu 9 Apr, 2020',
            nights: '1',
            startTime: '2020-04-09T14:00:00+10:00',
          },
        ],
      },
      promotion: {
        campaignCode: '',
      },
      inclusions: [],
      pointsEarned: {
        qffPoints: {
          total: 1200,
        },
        maxQffEarnPpd: 3,
      },
    },
    roomType: { name: 'Superior Room', maxOccupantCount: 2 },
    property: {
      id: '111',
      name: 'Hilton',
      rating: '3.5',
      ratingType: 'AAA',
      images: [
        {
          src: 'original-image.jpg',
          srcSet: 'large-image.jpg 2x, original-image.jpg 1x',
          alt: 'image',
          thumbnail: 'small-image.jpg',
          index: 0,
          total: 1,
        },
      ],
      mainImage: {
        urlSmall: 'small-image.jpg',
        urlMedium: 'med-image.jpg',
        urlLarge: 'large-image.jpg',
        urlOriginal: 'original-image.jpg',
        caption: 'image',
      },
      address: {
        suburb: 'Bangkok',
      },
    },
  };
});

const render = () =>
  mountUtils(<SearchResultDetail result={result} cardIndex={cardIndex} />, { decorators: { store: true, theme: true, router: true } });

describe('campaign messages', () => {
  describe('Currency is cash', () => {
    it('should contains <PointsPerDollar>', () => {
      const { find } = render();
      expect(find('PointsPerDollar')).toExist();
    });
  });
  describe('Currency is not cash', () => {
    beforeEach(() => {
      result.offer.type = 'special';
      result.offer.charges.total.currency = 'PTS';
    });

    it('renders the component with the correct props', () => {
      expect(render().find('CampaignPriceMessage')).toHaveProp({
        currency: 'PTS',
        offerType: 'special',
      });
    });
  });
});

describe('property image gallery', () => {
  it('has the expected props', () => {
    expect(render().find('SearchImageGallery')).toHaveProp({ images: result.property.images });
  });

  it('renders the image placeholder if a hotel has no images', () => {
    result.property.mainImage = null;

    expect(render().find('SearchImageGallery')).not.toExist();
    expect(render().find('ImageFallback')).toExist();
  });
});

describe('The Property details', () => {
  it('renders the property suburb', () => {
    expect(render().find('[data-testid="property-suburb"]').children().at(0)).toHaveText('Bangkok');
  });

  it('renders the property name', () => {
    expect(render().find('[data-testid="property-name"]').children().at(0)).toHaveText('Hilton');
  });
});

describe('The Offer details', () => {
  describe('number of nights', () => {
    it('it renders the number of nights', () => {
      expect(render().find('[data-testid="number-of-nights"]').children().at(0)).toHaveText('1 night from ');
    });
  });

  describe('total', () => {
    it('renders the total cost', () => {
      expect(render().find(Currency)).toHaveProp({
        amount: result.offer.charges.total.amount,
        currency: result.offer.charges.total.currency,
      });
    });

    it('does not render the * after the currency', () => {
      expect(render().findByTestId('pts-asterisk')).not.toExist();
    });
  });

  describe('when currency is points', () => {
    it('does not render the currency in parenthesis', () => {
      result.offer.charges.total.currency = 'PTS';
      expect(render().find('Text[data-testid="number-of-nights"]').text()).not.toMatch(/\(*.\)/);
    });

    it('renders PTS* after the currency', () => {
      result.offer.charges.total.currency = 'PTS';

      expect(render().findByTestId('points-plus-asterisk').text()).toEqual('PTS*');
    });
  });

  describe('when currency is points and campaignCode contains classic', () => {
    it('renders a classic badge', () => {
      result.offer.charges.total.currency = 'PTS';
      result.offer.type = 'classic';
      expect(render().find('Icon[data-testid="classic-ribbon"]').exists()).toEqual(true);
    });
  });
});

describe('The PromotionalSash', () => {
  describe('with no global campaign', () => {
    describe('with no offer.promotion', () => {
      beforeEach(() => {
        mocked(getCampaignDefaultSash).mockReturnValue(triplePointOfferName);
      });

      it('does NOT render the sash', () => {
        expect(render().find(PromotionalSash).exists()).toEqual(false);
      });
    });

    describe('with offer.promotion', () => {
      beforeEach(() => {
        result.offer.promotion = {
          name: genericOfferName,
        };
      });

      it('renders sash with the correct promotion name', () => {
        expect(render().find('[data-testid="promotional-sash"]')).toHaveProp({ promotionName: result.offer.promotion.name });
      });

      describe('when luxOffer is TRUE', () => {
        describe('and the promotion name is FEATURED', () => {
          beforeEach(() => {
            result.offer.promotion = {
              name: 'Featured',
            };
            result.offer.luxOffer = true;
          });

          it('renders sash with both offers name', () => {
            expect(render().find('[data-testid="promotional-sash"]')).toHaveProp({ promotionName: `Featured \u2022 Luxury Offer` });
          });
        });

        describe('and the promotion is NOT FEATURED', () => {
          beforeEach(() => {
            result.offer.promotion = {
              name: 'Luxury Offer',
            };
            result.offer.luxOffer = true;
          });

          it('renders sash with Luxury Offer', () => {
            expect(render().find('[data-testid="promotional-sash"]')).toHaveProp({ promotionName: 'Luxury Offer' });
          });
        });
      });
    });
  });

  describe('with a global campaign', () => {
    beforeEach(() => {
      mocked(getCampaignDefaultSash).mockReturnValue(triplePointOfferName);
    });

    describe('with no CLASSIC REWARD offer', () => {
      beforeEach(() => {
        result.offer.type = 'NOT classic';
      });

      describe('and with no offer.promotion', () => {
        beforeEach(() => {
          result.offer.promotion = null;
        });

        it('renders sash with the correct promotion name', () => {
          expect(render().find('PromotionalSash')).toHaveProp({ promotionName: triplePointOfferName });
        });
      });

      describe('and with offer.promotion', () => {
        beforeEach(() => {
          result.offer.promotion = {
            name: genericOfferName,
          };
        });

        it('renders sash with the correct promotion name', () => {
          expect(render().find('PromotionalSash')).toHaveProp({ promotionName: triplePointOfferName });
        });
      });
    });

    describe('with CLASSIC REWARD offer', () => {
      beforeEach(() => {
        result.offer.type = 'classic';
      });

      describe('with offer.promotion', () => {
        beforeEach(() => {
          result.offer.promotion = {
            name: classicOfferName,
          };
        });

        it('renders sash with the correct classic reward name', () => {
          expect(render().find('PromotionalSash')).toHaveProp({ promotionName: classicOfferName });
        });
      });
    });
  });
});

describe('The star rating', () => {
  describe('when the star rating is 0', () => {
    it('does not render the star rating', () => {
      result.property.rating = 0;
      expect(render().find('StarRating')).not.toExist();
    });
  });

  describe('when the star rating is not 0', () => {
    it('renders the star rating', () => {
      result.property.rating = 4;
      expect(render().find('StarRating')).toHaveProp({ rating: 4 });
    });
  });
});

describe('The Tripadvisor rating', () => {
  describe('when no customerRatings exist', () => {
    it('does not render the TripAdvisorRating', () => {
      expect(render().find('TripAdvisorRating')).not.toExist();
    });
  });

  describe('when the customerRatings exist', () => {
    it('renders the TripAdvisorRating', () => {
      result.property.customerRatings = [{ source: 'trip_advisor' }];
      expect(render().find('TripAdvisorRating').at(0)).toHaveProp({ rating: result.property.customerRatings[0] });
    });
  });
});

describe('Inclusions', () => {
  it('only renders if the Inclusions exist', () => {
    expect(render().find('InclusionsNew')).toHaveLength(0);
    result.offer.inclusions = [{ code: 'internet' }];
    expect(render().find('InclusionsNew')).toHaveLength(1);
  });
});

describe('The cancellation refund summary', () => {
  it('is rendered', () => {
    expect(render().find('CancellationRefundSummary').exists()).toEqual(true);
  });
});

describe('with a missing property image', () => {
  beforeEach(() => {
    result.property.mainImage = null;
  });

  it('has the expected src', () => {
    expect(render().find('ImageFallback').props().src).toBeUndefined();
  });

  it('has the expected srcSet', () => {
    expect(render().find('ImageFallback').props().srcSet).toBeUndefined();
  });
});

describe('when offer does not include points earn details', () => {
  beforeEach(() => {
    result.offer.pointsEarned = undefined;
  });

  it('renders without error', () => {
    expect(render).not.toThrow();
  });
});

describe('when qantas-hotels-available-rooms-message experiment', () => {
  describe('is OFF', () => {
    it('does not render RoomsAvailabilityMessage', () => {
      const { find } = render();

      expect(find('RoomsAvailabilityMessage')).not.toExist();
    });
  });
  describe('is ON', () => {
    beforeEach(() => {
      mocked(useAvailableRoomsMessage).mockReturnValue({
        isReady: true,
        isAvailableRoomsMessageEnabled: true,
        max_rooms_cutoff: 5,
      });
    });
    it('renders RoomsAvailabilityMessage', () => {
      const { find } = render();

      expect(find('RoomsAvailabilityMessage')).toExist();
    });
  });
});

describe('The Qantas Full Points experiment', () => {
  describe('when useShowFullPointsToggle is not ready', () => {
    beforeEach(() => {
      mocked(useShowFullPointsToggle).mockReturnValue({ showFullPoints: false, isReady: false });
    });

    it('does not render PointsEarnSummary', () => {
      const { find } = render();

      expect(find('PointsEarnSummary')).not.toExist();
    });

    it('renders PointsPerDollar', () => {
      const { find } = render();

      expect(find('PointsPerDollar')).toExist();
    });
  });

  describe('when useShowFullPointsToggle is ready', () => {
    describe('and showFullPoints is false', () => {
      beforeEach(() => {
        mocked(useShowFullPointsToggle).mockReturnValue({ showFullPoints: false, isReady: true });
      });

      it('does not render PointsEarnSummary', () => {
        const { find } = render();

        expect(find('PointsEarnSummary')).not.toExist();
      });

      it('renders PointsPerDollar', () => {
        const { find } = render();

        expect(find('PointsPerDollar')).toExist();
      });
    });

    describe('and showFullPoints is true', () => {
      beforeEach(() => {
        mocked(useShowFullPointsToggle).mockReturnValue({ showFullPoints: true, isReady: true });
      });

      it('renders PointsEarnSummary', () => {
        const { find } = render();

        expect(find('PointsEarnSummary')).toExist();
      });

      it('does not render PointsPerDollar', () => {
        const { find } = render();

        expect(find('PointsPerDollar')).not.toExist();
      });
    });
  });
});

describe('PriceStrikethrough', () => {
  describe('when the qantas-hotels-price-strikethrough flag is OFF', () => {
    it('does not render the PriceStrikethrough', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });

  describe('when the qantas-hotels-price-strikethrough flag is ON', () => {
    beforeEach(() => {
      mocked(usePriceStrikethrough).mockReturnValue({
        isReady: true,
        isPriceStrikethroughEnabled: true,
      });
    });

    it('renders PriceStrikethrough with the correct props', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).toHaveProp({
        price: result.offer.charges.strikethrough.price,
      });
    });

    it('does not render PriceStrikethrough when price strikethrough is not available', () => {
      result.offer.type = 'classic';
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });

    it('does not render PriceStrikethrough when offer type is classic', () => {
      result.offer.charges.strikethrough = { price: null, discount: null, percentage: null };
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });
});
