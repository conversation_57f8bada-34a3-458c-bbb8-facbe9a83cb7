import { PAYMENT_METHODS } from 'lib/enums/payment';
import { PAYMENT_CURRENCIES } from 'config/constants';
import { getQuotePriceStrikethrough } from './helper';

const mockedCharges = {
  payableAtBooking: {
    total: {
      amount: '421.06',
      pointsStrikethrough: 71160,
      points: 58831,
      currency: 'AUD',
    },
  },
  strikethrough: {
    price: {
      amount: '472.00',
      currency: 'AUD',
    },
  },
};

describe('getQuotePriceStrikethrough', () => {
  describe('when charges is null or undefined', () => {
    it('returns null for null charges', () => {
      expect(getQuotePriceStrikethrough(null, PAYMENT_METHODS.CASH)).toBe(null);
    });

    it('returns null for undefined charges', () => {
      expect(getQuotePriceStrikethrough(undefined, PAYMENT_METHODS.CASH)).toBe(null);
    });
  });

  describe('when in cash mode', () => {
    it('returns the charges.strikethrough.price', () => {
      expect(getQuotePriceStrikethrough(mockedCharges, PAYMENT_METHODS.CASH)).toStrictEqual(mockedCharges.strikethrough.price);
    });

    it('returns null when strikethrough.price is not available', () => {
      const chargesWithoutStrikethrough = {
        payableAtBooking: mockedCharges.payableAtBooking,
      };
      expect(getQuotePriceStrikethrough(chargesWithoutStrikethrough, PAYMENT_METHODS.CASH)).toBe(null);
    });
  });

  describe('when in points mode', () => {
    it('returns the constructed price from payableAtBooking', () => {
      expect(getQuotePriceStrikethrough(mockedCharges, PAYMENT_METHODS.POINTS)).toStrictEqual({
        amount: mockedCharges.payableAtBooking.total.pointsStrikethrough,
        currency: PAYMENT_CURRENCIES.POINTS,
      });
    });

    it('returns null when pointsStrikethrough is not available', () => {
      const chargesWithoutPoints = {
        payableAtBooking: {
          total: {
            points: 58831,
            // pointsStrikethrough is missing
          },
        },
      };
      expect(getQuotePriceStrikethrough(chargesWithoutPoints, PAYMENT_METHODS.POINTS)).toBe(null);
    });
  });
});
