import React, { useCallback } from 'react';
import { Box, Flex, Hide, Icon, NakedButton, Heading, Text } from '@qga/roo-ui/components';
import { useFeature } from '@optimizely/react-sdk';
import get from 'lodash/get';
import keyBy from 'lodash/keyBy';
import { Flag, OfferWrapper } from './primitives';
import { ORDERED_INCLUSIONS_WHITELIST } from './constants';
import ExpandedClickableArea from 'components/ExpandedClickableArea';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import { useDataLayer } from 'hooks/useDataLayer';
import PromotionalSashNew from 'components/PromotionalSashNew';
import type { Offer, Inclusion } from 'types/property';
import CancellationTooltip from 'components/CancellationTooltip/CancellationTooltip';
import CancellationRefundModal from 'components/CancellationRefundModal';
import NightsAndGuests from 'components/NightsAndGuests';
import { useBreakpoints } from 'hooks/useBreakpoints';
import OfferDepositPayMessage from './OfferDepositPayMessage';
import OfferInclusions from './OfferInclusions';
import { VALUE_ADD_SASH } from 'config/constants';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import PriceStrikethrough from 'components/PriceStrikethrough';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';

interface OfferSummaryProps {
  offer: Offer;
  toggleOfferExpanded: (value: boolean) => void;
}

const OfferSummary = ({ offer, toggleOfferExpanded }: OfferSummaryProps) => {
  const {
    allocationsAvailable,
    name: offerName,
    charges,
    inclusions,
    type: offerType,
    cancellationPolicy,
    depositPay,
    valueAdds = [],
  } = offer;
  const promotionName = get(offer, 'promotion.name');
  const { total } = charges;
  const isDepositPay = depositPay?.depositPayable;
  const currency = get(charges, 'total.currency');
  const { emitInteractionEvent } = useDataLayer();
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const isCurrencyPoints = currency === 'PTS';
  const isClassic = promotionName === 'Classic Reward';
  const inclusionsByKey = keyBy(inclusions, 'code');

  const sashPromotionName = promotionName ?? VALUE_ADD_SASH;
  const showPromotionalSashNew = promotionName || valueAdds?.length > 0;
  const [isGlobalInclusionsEnabled] = useFeature('qantas_hotels_global_inclusions');
  let orderedInclusions = isGlobalInclusionsEnabled
    ? inclusions
    : ORDERED_INCLUSIONS_WHITELIST.reduce<Inclusion[]>((accum, { code, icon }) => {
        const inclusion = inclusionsByKey[code];
        if (!inclusion) return accum;
        return [...accum, { ...inclusion, icon }];
      }, []);
  const valueAddsAndInclusions = () => {
    if (isMobile && valueAdds?.length > 0 && orderedInclusions) {
      orderedInclusions = [
        {
          icon: 'welcomeGift',
          description: 'value add',
          name: 'value adds',
          code: 'welcome_gift',
        },
        ...orderedInclusions,
      ];
    }
  };
  valueAddsAndInclusions();

  const limitedOrderedInclusionsMobile = orderedInclusions
    ? orderedInclusions.length > 7
      ? orderedInclusions?.slice(0, 7)
      : orderedInclusions
    : null;

  const onExpandOffer = useCallback(() => {
    toggleOfferExpanded(true);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Expanded' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  const { isAvailableRoomsMessageEnabled, max_rooms_cutoff } = useAvailableRoomsMessage();

  const availableRoomsMaxCutoff = max_rooms_cutoff ?? 5;
  const showAvailableRooms =
    isAvailableRoomsMessageEnabled && allocationsAvailable && allocationsAvailable > 0 && allocationsAvailable <= availableRoomsMaxCutoff;
  const { isPriceStrikethroughEnabled } = usePriceStrikethrough();
  const strikethroughPrice = charges?.strikethrough?.price;
  const isPriceStrikeAvailable = !!strikethroughPrice?.amount;
  const showPriceStrikethrough = isPriceStrikeAvailable && isPriceStrikethroughEnabled && !isClassic;

  return (
    <ExpandedClickableArea>
      <OfferWrapper flexDirection="column" data-testid="offer-card-new">
        {showPromotionalSashNew && (
          <Box position="absolute" left={0} top={0}>
            <PromotionalSashNew promotionName={sashPromotionName} type="corner" />
          </Box>
        )}
        <Flex data-testid="expand-icon" flexDirection={'row'} justifyContent="flex-end">
          <NakedButton
            onClick={onExpandOffer}
            aria-label="Expand offer details"
            data-expanded-clickable-area-target
            p={0}
            data-testid="expand-offer-summary"
          >
            <Icon name="expandMore" size={24} />
          </NakedButton>
        </Flex>
        <Flex data-testid="offer-card-body" flexDirection={['column', 'row', 'row']} justifyContent="space-between">
          <Flex mb={[4, 4]} data-testid="extra-flex-inclusions" flexDirection="column" width={['100%', '50%']} justifyContent="flex-start">
            <Flex justifyContent={isMobile ? 'space-between' : 'flex-start'}>
              <Heading.h3 mb={[0, 0]} fontSize="md" lineHeight="normal" color="greys.charcoal">
                {offerName}
              </Heading.h3>
            </Flex>
            <Box color="greys.charcoal">
              <Flex flexDirection="column" data-testid="inclusions-preview-wrapper-desktop">
                <Hide xs>
                  <OfferInclusions inclusions={inclusions} valueAdds={valueAdds} onClick={onExpandOffer} />
                </Hide>
              </Flex>
              <Flex flexDirection="row" data-testid="inclusions-preview-wrapper-mobile">
                {isMobile &&
                  limitedOrderedInclusionsMobile &&
                  limitedOrderedInclusionsMobile.map(({ code, icon }) => (
                    <Box pb={2} key={code}>
                      <Flag icon={icon} color="greys.charcoal" />
                    </Box>
                  ))}
              </Flex>
            </Box>
          </Flex>
          <Flex
            data-testid="right-column-offer-card"
            flexDirection={['column']}
            alignItems={['flex-start', 'flex-end', 'flex-end']}
            width={['100%', '360px', '360px']}
            pl={[0, 6, 0]}
          >
            <Flex flexDirection="column" data-testid="collapsed-offers" alignSelf={'flex-start'}>
              <Flex flexDirection="column">
                <Flex
                  flexDirection="column"
                  justifyContent="center"
                  alignItems="space-between"
                  borderLeft={3}
                  borderColor={'red'}
                  pl={2}
                  mb={3}
                >
                  <NightsAndGuests />

                  <Flex flexDirection="row" alignItems="baseline" flexWrap="wrap">
                    <Flex alignItems="center">
                      {isClassic && <Icon name="ribbon" mr="2" color="greys.charcoal" />}
                      <Currency
                        amount={total.amount}
                        currency={total.currency}
                        roundToCeiling
                        fontSize={'32px'}
                        fontWeight={600}
                        hideCurrency={true}
                        color="greys.charcoal"
                        data-testid="total-to-pay-new"
                        alignCurrency="superscript"
                      />
                      {!showPriceStrikethrough && (
                        <Text pl={1} pb="10px" fontWeight={'bold'}>
                          {currency}
                          {isCurrencyPoints && <sup data-testid="asterisk">*</sup>}
                        </Text>
                      )}
                      {showPriceStrikethrough && <PriceStrikethrough price={strikethroughPrice} ml={2} mt={isCurrencyPoints ? 2 : 3} />}
                    </Flex>
                  </Flex>

                  <Box>
                    <CampaignPriceMessage
                      currency={total.currency}
                      offerType={offerType}
                      fallback
                      color={showAvailableRooms ? 'greys.steel' : undefined}
                    />
                  </Box>
                </Flex>
                {showAvailableRooms && <RoomsAvailabilityMessage allocationsAvailable={allocationsAvailable} isOffer={true} />}
                {!isMobile && (
                  <>
                    <CancellationTooltip cancellationPolicy={cancellationPolicy} />
                    {isDepositPay && (
                      <Box>
                        <OfferDepositPayMessage depositPay={depositPay} />
                      </Box>
                    )}
                  </>
                )}
              </Flex>
            </Flex>

            {isMobile && (
              <>
                <Box mt={2}>
                  <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize={['xs', 'sm']} />
                </Box>
                {isDepositPay && (
                  <Box mb={[2, 10, 10]}>
                    <OfferDepositPayMessage depositPay={depositPay} />
                  </Box>
                )}
              </>
            )}
          </Flex>
        </Flex>
      </OfferWrapper>
    </ExpandedClickableArea>
  );
};

export default OfferSummary;
