import React from 'react';
import { mountUtils, mocked } from 'test-utils';
import { StarRating } from '@qga/roo-ui/components';
import { useFeature } from '@optimizely/react-sdk';
import PropertyHeader from './PropertyHeader';
import TripAdvisorRating from 'components/TripAdvisorRating';
import TripAdvisorReviewsModal from 'components/TripAdvisorReviewsModal';
import ResultLoader from 'components/Loader/ResultLoader';
import { getIsLoading } from 'store/property/propertySelectors';
import { getIsLoading as getIsLoadingExclusiveOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import LoaderSkeletonCard from './LoaderSkeletonCard';

jest.mock('components/TripAdvisorReviewsModal', () => 'trip-advisor-review-modal-mock');
jest.mock('components/PropertyPage/MapWithMarkerButton', () => jest.fn().mockImplementation(({ children }) => children));
jest.mock('components/TripAdvisorRating', () => jest.fn().mockReturnValue(null));
jest.mock('store/property/propertySelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('hooks/useRatingTooltip');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('FromPrice');
mountUtils.mockComponent('TripAdvisorReviewsModal');
mountUtils.mockComponent('SkeletonCard');

let property = {
  id: '1',
  name: 'The Grand Budapest',
  rating: 3.5,
  ratingType: 'AAA',
};

let tripAdvisorIgnoredProperty = {
  id: '2',
  name: 'The Crown Budapest',
  rating: 4.5,
  ratingType: 'AAA',
};

let tripAdvisorRating = {
  averageRating: 4,
  reviewCount: 1,
  locationId: 'locationId',
};

const decorators = { store: true, theme: true };
const render = () => mountUtils(<PropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />, { decorators });
const renderTripAdvisorIgnoredProperty = () =>
  mountUtils(<PropertyHeader property={tripAdvisorIgnoredProperty} tripAdvisorRating={tripAdvisorRating} />, { decorators });

describe('<PropertyHeader />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useFeature).mockReturnValue([true, { ignoredList: { ignore: [123, 456] } }]);
  });
  describe('when loading standard offer', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: 1,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when loading exclusive offer', () => {
    beforeEach(() => {
      getIsLoadingExclusiveOffers.mockReturnValue(true);
    });

    it('renders the ResultLoader', () => {
      const { find } = render();

      expect(find(ResultLoader)).toHaveProp({
        isLoading: true,
        skeletonResultCount: 1,
        skeletonCardComponent: LoaderSkeletonCard,
      });
    });
  });

  describe('when not loading', () => {
    beforeEach(() => {
      getIsLoading.mockReturnValue(false);
      getIsLoadingExclusiveOffers.mockReturnValue(false);
    });
    it('renders the property name', () => {
      const { findByTestId } = render();
      expect(findByTestId('property-name').text()).toEqual(property.name);
    });

    it('renders the star rating', () => {
      const { find } = render();
      expect(find(StarRating)).toHaveProp({ rating: property.rating, ratingType: property.ratingType });
    });

    it('does not render the star rating when rating is 0', () => {
      const zeroRatingProperty = { ...property, rating: 0 };

      const { find } = mountUtils(<PropertyHeader property={zeroRatingProperty} tripAdvisorRating={tripAdvisorRating} />, { decorators });

      expect(find(StarRating)).not.toExist();
    });

    it('renders <FromPrice />', () => {
      const { find } = render();
      expect(find('FromPrice')).toExist();
    });

    describe('qantas-hotels-tripadvisor-modal feature', () => {
      describe('when is on', () => {
        it('renders the TripAdvisorRating modal', () => {
          const { find, findByTestId } = render();
          findByTestId('trip-advisor-button').simulate('click');
          expect(find(TripAdvisorReviewsModal)).toExist();
        });

        it('renders the TripAdvisorRating with the displayReviews', () => {
          const { find } = render();
          expect(find(TripAdvisorRating)).toHaveProp({ rating: tripAdvisorRating, displayReviews: true });
        });

        describe('and the property is in the ignoreList', () => {
          beforeEach(() => {
            mocked(useFeature).mockReturnValue([true, { ignoredList: { ignore: [1, 2] } }]);
          });

          it('does not render the TripAdvisorRating modal', () => {
            const { find, findByTestId } = renderTripAdvisorIgnoredProperty();
            findByTestId('trip-advisor-button').simulate('click');
            expect(find(TripAdvisorReviewsModal)).not.toExist();
          });
        });
      });

      describe('when is off', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([false, { ignoredList: { ignore: [123, 456] } }]);
        });
        it('does not render the TripAdvisorRating modal', () => {
          const { find, findByTestId } = render();
          findByTestId('trip-advisor-button').simulate('click');
          expect(find(TripAdvisorReviewsModal)).not.toExist();
        });

        it('renders the TripAdvisorRating with the displayReviews', () => {
          const { find } = render();
          expect(find(TripAdvisorRating)).toHaveProp({ rating: tripAdvisorRating, displayReviews: false });
        });
        it('should not contain onClick', () => {
          const { findByTestId } = render();
          expect(findByTestId('trip-advisor-button')).toHaveProp({ onClick: undefined });
        });
      });
    });
  });
});
