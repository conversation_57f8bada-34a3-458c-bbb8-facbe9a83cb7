import React from 'react';
import { Box, Text, Flex } from '@qga/roo-ui/components';
import { Flag } from './primitives';
import TextButton from 'components/TextButton';
import { ORDERED_INCLUSIONS_WHITELIST } from './constants';
import keyBy from 'lodash/keyBy';
import { createValueAddsObj } from './OfferDetails.helper';
import { useFeature } from '@optimizely/react-sdk';
import type { Inclusion } from 'types/property';

const INCLUSIONS_PREVIEW_AMOUNT = 3;

interface OfferInclusionsProps {
  inclusions?: Inclusion[];
  valueAdds?: string[];
  onClick: () => void;
}

const OfferInclusions = React.memo(({ inclusions = [{}], valueAdds = [], onClick }: OfferInclusionsProps) => {
  const remainingInclusions = inclusions ? inclusions?.length + valueAdds?.length - 3 : 0;
  const showViewAll = inclusions?.length + valueAdds?.length > INCLUSIONS_PREVIEW_AMOUNT;
  const [isGlobalInclusionsEnabled] = useFeature('qantas_hotels_global_inclusions');
  const inclusionsByKey = inclusions ? keyBy(inclusions, 'code') : null;
  const orderedInclusions = isGlobalInclusionsEnabled
    ? inclusions
    : inclusions
      ? ORDERED_INCLUSIONS_WHITELIST.reduce((accum, { code, icon }) => {
          const inclusion = inclusionsByKey ? inclusionsByKey[code] : null;
          if (!inclusion) return accum;
          return [...accum, { ...inclusion, icon }];
        }, [])
      : null;
  const valueAddsAndInclusions = valueAdds ? createValueAddsObj(valueAdds).concat(orderedInclusions) : orderedInclusions;
  const orderedInclusionsPreview = valueAddsAndInclusions ? valueAddsAndInclusions.slice(0, INCLUSIONS_PREVIEW_AMOUNT) : null;

  return (
    <Box color="greys.charcoal" mb={2}>
      <Flex flexDirection="column" data-testid="inclusions-preview-wrapper">
        {orderedInclusionsPreview
          ? orderedInclusionsPreview.map(({ icon, description }, i) => (
              <Flag key={i} icon={icon}>
                <Text fontSize="16px" fontWeight={icon === 'welcomeGift' ? 'bold' : 'normal'}>
                  {description}
                </Text>
              </Flag>
            ))
          : null}
      </Flex>
      {showViewAll && (
        <TextButton onClick={onClick} fontSize="16px" mt="3" data-testid="view-all-inclusions">
          {`+ ${remainingInclusions} more ${remainingInclusions === 1 ? 'inclusion' : 'inclusions'}`}
        </TextButton>
      )}
    </Box>
  );
});

export default OfferInclusions;
