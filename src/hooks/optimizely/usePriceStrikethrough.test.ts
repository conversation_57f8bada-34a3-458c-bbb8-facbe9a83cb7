import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import usePriceStrikethrough from './usePriceStrikethrough';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

const defaultMockDecision = {
  enabled: false,
};

describe('usePriceStrikethrough', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([defaultMockDecision, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false }, false]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current).toEqual({
      isReady: false,
      isPriceStrikethroughEnabled: false,
    });
  });

  it('returns isPriceStrikethroughEnabled as true when feature flag is on', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true }, true]);

    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.isPriceStrikethroughEnabled).toBe(true);
  });

  it('returns isPriceStrikethroughEnabled as false when feature flag is off', () => {
    const { result } = renderHook(() => usePriceStrikethrough());

    expect(result.current.isPriceStrikethroughEnabled).toBe(false);
  });
});
