import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { useFeature } from '@optimizely/react-sdk';
import { Box, Flex, Heading, Hide, Text } from '@qga/roo-ui/components';
import OfferDetailsModal from './OfferDetailsModal';
import OfferDetailsTooltip from './OfferDetailsTooltip';
import { ORDERED_INCLUSIONS_WHITELIST } from './constants';
import keyBy from 'lodash/keyBy';
import { Flag } from './primitives';
import { createValueAddsObj } from './OfferDetails.helper';

const OfferDetails = memo(({ offer }) => {
  const { inclusions, description: offerDescription, name: offerName, valueAdds = [] } = offer;
  const [isGlobalInclusionsEnabled] = useFeature('qantas_hotels_global_inclusions');
  const inclusionsByKey = keyBy(inclusions, 'code');
  const orderedInclusions = isGlobalInclusionsEnabled
    ? inclusions
    : ORDERED_INCLUSIONS_WHITELIST.reduce((accum, { code, icon }) => {
        const inclusion = inclusionsByKey[code];
        if (!inclusion) return accum;
        return [...accum, { ...inclusion, icon }];
      }, []);
  const valueAddsAndInclusions = valueAdds ? createValueAddsObj(valueAdds).concat(orderedInclusions) : orderedInclusions;

  return (
    <Flex flexDirection="column" data-testid="offer-details">
      <Heading.h3 display="block" mb={0} fontSize="md" lineHeight="normal">
        {offerName}
      </Heading.h3>
      <Box height="auto" color="greys.charcoal">
        {valueAddsAndInclusions.map(({ icon, description }, i) => (
          <Box key={i} data-testid="internet">
            <Flag icon={icon} color="greys.charcoal">
              <Text fontSize="16px" fontWeight={icon === 'welcomeGift' ? 'bold' : 'normal'}>
                {description}
              </Text>
            </Flag>
          </Box>
        ))}
        <Box pt={2}>
          <Hide sm xs>
            <OfferDetailsTooltip offerDescription={offerDescription} offerName={offerName} />
          </Hide>
        </Box>
        <Hide md lg>
          <OfferDetailsModal offerDescription={offerDescription} offerName={offerName} />
        </Hide>
      </Box>
    </Flex>
  );
});

OfferDetails.propTypes = {
  offer: PropTypes.object.isRequired,
};

OfferDetails.displayName = 'OfferDetails';

export default OfferDetails;
