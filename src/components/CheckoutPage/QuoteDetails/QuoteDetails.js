import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { useFeature } from '@optimizely/react-sdk';
import { getQuote, getStayDates, getOccupants } from 'store/quote/quoteSelectors';
import get from 'lodash/get';
import { Wrapper, Box, Text } from '@qga/roo-ui/components';
import PropertyDetails from './PropertyDetails';
import StayDates from './StayDates';
import PriceDetails from './PriceDetails';
import QuoteItem from './QuoteItem';
import CancellationRefundModal from 'components/CancellationRefundModal';
import OccupantsSummary from 'components/OccupantsSummary';
import PointsEarnSummary from './PointsEarnSummary';
import Inclusions from './Inclusions';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';

const QuoteDetails = ({ ...rest }) => {
  const [isGlobalInclusionsEnabled] = useFeature('qantas_hotels_global_inclusions');
  const { isAvailableRoomsMessageEnabled } = useAvailableRoomsMessage();
  const quote = useSelector(getQuote);
  const { checkIn, checkOut } = useSelector(getStayDates) || {};
  const occupants = useSelector(getOccupants);

  if (!quote) return null;

  const { property, offer, roomType } = quote;
  const inclusions = get(offer, 'inclusions');
  const hasInclusions = inclusions?.length > 0;

  const cancellationPolicy = get(offer, 'cancellationPolicy');
  const formattedInclusionDescriptions = (inclusions || []).map((inclusion) => inclusion.description).join(', ');

  const allocationsAvailable = get(offer, 'allocationsAvailable', 0);

  return (
    <Wrapper {...rest}>
      <Box mb={[0, 10]}>
        <PropertyDetails property={property} roomType={roomType} offer={offer} />
        {offer && (
          <Fragment>
            <Box boxShadow="hard" borderRadius="defaultRoundBottomOnly" overflow="hidden">
              <Box px={[3, 8]} bg="white" pb={4}>
                {isGlobalInclusionsEnabled && hasInclusions && (
                  <QuoteItem borderBottom={1} py={0}>
                    <Inclusions inclusions={inclusions} />
                  </QuoteItem>
                )}
                <QuoteItem borderBottom={0}>
                  <OccupantsSummary fontSize="base" occupants={occupants} />
                  <StayDates checkIn={checkIn} checkOut={checkOut} />
                  <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize="sm" mt={4} flexDirection="row" />
                </QuoteItem>
                <QuoteItem borderBottom={0} py={0}>
                  {formattedInclusionDescriptions.length > 0 && !isGlobalInclusionsEnabled && (
                    <Text fontSize="sm" display="block" mb={2} data-testid="inclusion-descriptions">
                      {formattedInclusionDescriptions}
                    </Text>
                  )}
                  <PointsEarnSummary pointsEarned={offer.pointsEarned} />
                  {isAvailableRoomsMessageEnabled && <RoomsAvailabilityMessage allocationsAvailable={allocationsAvailable} mt={1} />}
                </QuoteItem>
              </Box>
              <PriceDetails
                property={property}
                offer={offer}
                checkIn={checkIn}
                checkOut={checkOut}
                p={6}
                bg="lightBlue"
                borderTop={1}
                borderColor="greys.alto"
              />
            </Box>
          </Fragment>
        )}
      </Box>
    </Wrapper>
  );
};

export default QuoteDetails;
