import React from 'react';
import OfferPriceBox from './OfferPriceBox';
import { mountUtils, mocked } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';
import OfferPointsPaySlider from './OfferPointsPaySlider';
import { getLevels } from 'store/pointsConversion/pointsConversionSelectors';
import { getPointsLevels } from 'store/pointsBurnTiers/pointsBurnSelectors';
import { TIER_9 } from 'test-utils/points/conversionTiers';
import { getQueryCheckIn, getQueryCheckOut } from 'store/router/routerSelectors';
import { getPropertyId } from 'store/property/propertySelectors';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough';

jest.mock('./OfferPointsPaySlider', () => () => null);
jest.mock('store/pointsConversion/pointsConversionSelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('store/router/routerSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('store/campaign/campaignSelectors');
jest.mock('hooks/optimizely/usePriceStrikethrough');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('OfferPointsPaySliderUpdated');
mountUtils.mockComponent('OfferCheckoutLink');
mountUtils.mockComponent('OfferPayableAtProperty');
mountUtils.mockComponent('CampaignPriceMessage');
mountUtils.mockComponent('OfferPointsPaySlider');
mountUtils.mockComponent('OfferGuestsText');
mountUtils.mockComponent('PriceStrikethrough');
jest.mock('store/pointsConversion/pointsConversionSelectors');

const defaultProps = {
  onCashPaymentAmountChange: jest.fn(),
  offerType: 'standard',
  checkoutUrl: 'http://checkoutpage.com',
  roomTypeId: '2',
  offerId: '3',
  offerName: 'Best Rate',
  roomName: 'Standard King',
  cancellationPolicy: {
    isNonrefundable: true,
    description: 'Non Refundable',
    cancellationWindows: [],
  },
  depositPay: {},
  charges: {
    total: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalBeforeDiscount: {
      amount: '159.00',
      currency: 'AUD',
    },
    totalDiscount: {
      amount: '0',
      currency: 'AUD',
    },
    payableAtBooking: {
      total: {
        amount: '149.00',
        currency: 'AUD',
      },
    },
    payableAtProperty: {
      total: {
        amount: '0',
        currency: 'AUD',
      },
    },
    strikethrough: {
      price: {
        amount: '179000',
        currency: 'AUD',
      },
    },
  },
};

const checkIn = new Date(2025, 3, 30);
const checkOut = new Date(2025, 4, 1);
const propertyId = '1';
const isPointPay = true;
const isNotPointPay = false;

const decorators = { theme: true, store: true, router: true };
const render = (props = {}) => mountUtils(<OfferPriceBox {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.clearAllMocks();
  getLevels.mockReturnValue(TIER_9);
  getPointsLevels.mockReturnValue(TIER_9);
  getQueryCheckIn.mockReturnValue(checkIn);
  getQueryCheckOut.mockReturnValue(checkOut);
  getPropertyId.mockReturnValue(propertyId);
  getIsPointsPay.mockReturnValue(isNotPointPay);
  mocked(usePriceStrikethrough).mockReturnValue({
    isReady: true,
    isPriceStrikethroughEnabled: false,
  });
  useAvailableRoomsMessage.mockReturnValue({
    isReady: true,
    isAvailableRoomsMessageEnabled: false,
    max_rooms_cutoff: 5,
  });
});

describe('OfferPriceBox', () => {
  beforeEach(() => {
    mocked(useFeature).mockReturnValue([false]);
  });

  describe('with in cash mode', () => {
    it('shows the OfferGuestsText component', () => {
      const { find } = render();
      expect(find('OfferGuestsText')).toExist();
    });

    it('shows the currency after the number of nights', () => {
      const { findByTestId } = render();
      expect(findByTestId('currency-symbol-not-from-price')).toExist();
    });

    it('Shows the price', () => {
      const { wrapper } = render();
      expect(wrapper).toIncludeText('$159');
    });

    it('Shows the currency', () => {
      const { wrapper } = render();
      expect(wrapper).toIncludeText('AUD');
    });

    it('does not show the * after the currency', () => {
      const { findByTestId } = render();
      expect(findByTestId('asterisk')).not.toExist();
    });

    it('renders the OfferPayableAtProperty', () => {
      const { find } = render();
      expect(find('OfferPayableAtProperty')).toHaveProp({
        payableAtProperty: defaultProps.charges.payableAtProperty.total,
        isIncludedInTotal: true,
      });
    });

    describe('OfferCheckoutLink', () => {
      it('renders the OfferCheckoutLink with the corrects props', () => {
        const { find } = render();

        expect(find('OfferCheckoutLink')).toHaveProp({
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          offerId: defaultProps.offerId,
          offerName: defaultProps.offerName,
          roomName: defaultProps.roomName,
        });
      });
    });
  });

  describe('when in points mode', () => {
    const props = {
      ...defaultProps,
      charges: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '2795',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: 0,
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '0',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '2795',
            currency: 'PTS',
          },
        },
      },
    };

    it('shows the OfferGuestsText component', () => {
      const { find } = render();
      expect(find('OfferGuestsText')).toExist();
    });
    it('Shows the price', () => {
      const { wrapper } = render(props);
      expect(wrapper).toIncludeText('2,795');
    });

    it('Shows the currency', () => {
      const { wrapper } = render(props);
      expect(wrapper).toIncludeText('PTS');
    });

    it('Shows the * after the currency', () => {
      const { wrapper } = render(props);
      expect(wrapper).toIncludeText('*');
    });

    it('renders the OfferPayableAtProperty', () => {
      const { find } = render(props);

      expect(find('OfferPayableAtProperty')).toHaveProp({
        payableAtProperty: props.charges.payableAtProperty.total,
        isIncludedInTotal: false,
      });
    });
  });

  describe('OfferCheckoutLink', () => {
    it('renders the OfferCheckoutLink with the corrects props', () => {
      const { find } = render();

      expect(find('OfferCheckoutLink')).toHaveProp({
        propertyId: propertyId,
        roomTypeId: defaultProps.roomTypeId,
        offerId: defaultProps.offerId,
        offerName: defaultProps.offerName,
        roomName: defaultProps.roomName,
      });
    });
  });

  describe('hasDiscount', () => {
    describe('when it is false', () => {
      it('renders the Currency components', () => {
        const { findByTestId } = render();

        expect(findByTestId('total-to-pay')).toExist();
      });

      it('renders the points campaign price message with the correct props', () => {
        const { find } = render(defaultProps);

        expect(find('CampaignPriceMessage')).toHaveProp({
          currency: 'AUD',
          offerType: 'standard',
        });
      });
    });
  });

  describe('when in points + pay mode', () => {
    beforeEach(() => {
      getIsPointsPay.mockReturnValue(isPointPay);
    });

    describe('when in cash mode', () => {
      it('renders the OfferPointsPaySlider with the correct props', () => {
        const { find } = render();

        expect(find(OfferPointsPaySlider)).toHaveProp({
          totalCash: defaultProps.charges.total,
          payableAtProperty: defaultProps.charges.payableAtProperty.total,
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          offerId: defaultProps.offerId,
          offerType: defaultProps.offerType,
        });
      });
    });

    describe('when in points mode', () => {
      const charges = {
        total: {
          amount: '27950',
          currency: 'PTS',
        },
        totalCash: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '30000',
          currency: 'PTS',
        },
        totalDiscount: {
          amount: '2050',
          currency: 'PTS',
        },
        payableAtProperty: {
          total: {
            amount: '10',
            currency: 'AUD',
          },
        },
        payableAtBooking: {
          total: {
            amount: '27950',
            currency: 'PTS',
          },
        },
      };

      it('renders the OfferPointsPaySlider', () => {
        const { find } = render({
          ...defaultProps,
          isPointsPay: true,
          charges,
        });

        expect(find(OfferPointsPaySlider)).toHaveProp({
          totalCash: charges.totalCash,
          payableAtProperty: charges.payableAtProperty.total,
          propertyId: propertyId,
          roomTypeId: defaultProps.roomTypeId,
          offerId: defaultProps.offerId,
          offerType: defaultProps.offerType,
        });
      });
    });

    it('does not show the currency after the number of nights', () => {
      const { findByTestId } = render({ isPointsPay: true });

      expect(findByTestId('currency-symbol')).not.toExist();
    });

    it('does not render the total price', () => {
      const { findByTestId } = render({ isPointsPay: true });

      expect(findByTestId('total-to-pay')).toExist();
    });

    it('does not render the OfferPayableAtProperty', () => {
      const { find } = render({ isPointsPay: true });

      expect(find('OfferPayableAtProperty')).toExist();
    });

    it('does not show the slider for classic offers', () => {
      const { find } = render({ isPointsPay: true, offerType: 'classic' });

      expect(find(OfferPointsPaySlider)).not.toExist();
    });

    it('does not show the slider for cash based payableAtBooking amounts less than 5000 points', () => {
      const { find } = render({
        isPointsPay: true,
        charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '25.00', currency: 'AUD' } } },
      });

      expect(find(OfferPointsPaySlider)).not.toExist();
    });

    it('does not show the slider for points based payableAtBooking amounts less than 5000 points', () => {
      const { find } = render({
        isPointsPay: true,
        charges: { ...defaultProps.charges, payableAtBooking: { total: { amount: '4999', currency: 'PTS' } } },
      });

      expect(find(OfferPointsPaySlider)).not.toExist();
    });
  });
});

describe('qantas-hotels-available-rooms-message experiment', () => {
  const props = {
    ...defaultProps,
    charges: {
      total: {
        amount: '2795',
        currency: 'PTS',
      },
      totalCash: {
        amount: '100',
        currency: 'AUD',
      },
      totalBeforeDiscount: {
        amount: '2795',
        currency: 'PTS',
      },
      totalDiscount: {
        amount: '0',
        currency: 'PTS',
      },
      payableAtProperty: {
        total: {
          amount: '0',
          currency: 'AUD',
        },
      },
      payableAtBooking: {
        total: {
          amount: '2795',
          currency: 'PTS',
        },
      },
    },
  };

  it('does not display message when experiment is OFF', () => {
    const { findByText } = render({ ...props, allocationsAvailable: 1 });

    expect(findByText('Hurry, we only have 1 left!')).not.toExist();
  });

  describe('when the qantas-hotels-available-rooms-message feature flag is ON', () => {
    beforeEach(() => {
      useAvailableRoomsMessage.mockReturnValue({
        isReady: true,
        isAvailableRoomsMessageEnabled: true,
        max_rooms_cutoff: 5,
      });
    });

    it('does not display message when allocations prop is missing', () => {
      const { findByText } = render({ ...props });

      expect(findByText('Hurry, we only have 1 room left!')).not.toExist();
    });

    it('does not display message when allocations is larger than max', () => {
      const { findByText } = render({ ...props, allocationsAvailable: 6 });

      expect(findByText('We only have 6 rooms left')).not.toExist();
    });

    it('displays message when experiment is ON', () => {
      const { findByText, reRender } = render({ ...props, allocationsAvailable: 1 });

      expect(findByText('Hurry, we only have 1 room left!')).toExist();

      mocked(useFeature).mockReturnValue([false]);

      reRender();

      expect(findByText('Hurry, we only have 1 room left!')).toExist();
    });
  });
});

describe('PriceStrikethrough', () => {
  describe('when the qantas-hotels-price-strikethrough flag is OFF', () => {
    it('does not render the PriceStrikethrough', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });

  describe('when the qantas-hotels-price-strikethrough flag is ON', () => {
    beforeEach(() => {
      mocked(usePriceStrikethrough).mockReturnValue({
        isReady: true,
        isPriceStrikethroughEnabled: true,
      });
    });

    it('renders PriceStrikethrough with the correct props', () => {
      const { find } = render();

      expect(find('PriceStrikethrough')).toHaveProp({
        price: defaultProps.charges.strikethrough.price,
      });
    });

    it('does not render PriceStrikethrough when price strikethrough is not available', () => {
      defaultProps.offerType = 'classic';
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });

    it('does not render PriceStrikethrough when offer type is classic', () => {
      defaultProps.charges.strikethrough = { price: null, discount: null, percentage: null };
      const { find } = render();

      expect(find('PriceStrikethrough')).not.toExist();
    });
  });
});
