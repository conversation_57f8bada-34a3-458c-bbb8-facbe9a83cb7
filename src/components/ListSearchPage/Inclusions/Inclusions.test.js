import React from 'react';
import { mountUtils } from 'test-utils';
import Inclusions from './Inclusions';
import { useFeature } from '@optimizely/react-sdk';

jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const inclusions = [
  { code: 'club_access', description: 'free club access', icon: 'incQfClub' },
  { code: 'internet', description: 'free internet', icon: 'incQfWifi' },
  { code: 'breakfast', description: 'breakfast for 2', icon: 'incQfBreakfast' },
  { code: 'parking', description: 'on-site parking', icon: 'incQfCar' },
];

const decorators = { theme: true };
const render = () => mountUtils(<Inclusions inclusions={inclusions} />, { decorators });

describe('The Inclusions', () => {
  describe('when useGlobalInclusions is false', () => {
    beforeEach(() => {
      useFeature.mockReturnValue([false]);
    });

    describe('when there are more than 3', () => {
      it('renders a maximum of 3', () => {
        const { find } = render();
        expect(find('Icon')).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          const { findByTestId } = render();
          expect(findByTestId('inclusion-old').first()).toHaveProp({ title: 'free internet' });
          expect(findByTestId('inclusion-old').last()).toHaveProp({ title: 'on-site parking' });
        });
      });

      describe('The Icons', () => {
        it('only renders from the whitelist in order', () => {
          const { find } = render();
          expect(find('Icon').at(0)).toHaveProp({ name: 'wifi' });
          expect(find('Icon').at(1)).toHaveProp({ name: 'restaurant' });
          expect(find('Icon').at(2)).toHaveProp({ name: 'directionsCar' });
        });
      });
    });
  });

  describe('when useGlobalInclusions is true', () => {
    beforeEach(() => {
      useFeature.mockReturnValue([true]);
    });

    describe('when there are more than 3', () => {
      it('renders a maximum of 3', () => {
        const { find } = render();
        expect(find('Icon')).toHaveLength(3);
      });

      describe('The Title', () => {
        it('is added from the description', () => {
          const { findByTestId } = render();
          expect(findByTestId('inclusion-old').first()).toHaveProp({ title: 'free club access' });
          expect(findByTestId('inclusion-old').last()).toHaveProp({ title: 'breakfast for 2' });
        });
      });

      describe('The Icons', () => {
        it('it renders the icons in the order the inclusions are received', () => {
          const { find } = render();
          expect(find('Icon').at(0)).toHaveProp({ name: 'incQfClub' });
          expect(find('Icon').at(1)).toHaveProp({ name: 'incQfWifi' });
          expect(find('Icon').at(2)).toHaveProp({ name: 'incQfBreakfast' });
        });
      });
    });
  });
});
