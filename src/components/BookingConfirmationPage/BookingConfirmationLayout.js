import PropTypes from 'prop-types';
import React, { Fragment, useRef } from 'react';
import { useMount, useUnmount } from 'react-use';
import get from 'lodash/get';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';
import styled from '@emotion/styled';
import { Box, Flex, Heading, Hide, Icon, LoadingIndicator } from '@qga/roo-ui/components';
import PageBlock from 'components/PageBlock';
import { BOOKING_CONFIRMATION_PAGE_META } from 'lib/enums/bookingConfirmation';
import BookingSummary from './BookingSummary';
import GuestDetails from './GuestDetails';
import HotelInformation from './HotelInformation';
import ReservationDates from './ReservationDates';
import PropertyDescription from './PropertyDescription';
import CheckInInformation from './CheckInInformation';
import RoomDetails from './RoomDetails';
import PaymentDetails from './PaymentDetails';
import CancellationPolicy from './CancellationPolicy';
import PropertyPolicies from './PropertyPolicies';
import QHContactDetails from './QHContactDetails';
import ImportantInformationFooter from './ImportantInformationFooter';
import LoadingError from './LoadingError';
import BookingConfirmationFaqsLinks from './BookingConfirmationFaqsLinks';
import BookingConfirmationContactUsLinks from './BookingConfirmationContactUsLinks';
import { getBookingReference, getProperty, getIsLoading, getLoadingError } from 'store/booking/bookingSelectors';
import { mediaQuery } from 'lib/styledSystem';
import { HOTELS_BRAND_NAME, QFF_ACCOUNT_MANAGEMENT, ACCOUNT_MANAGEMENT_TYPES, TRAVEL_INSURANCE_CROSS_SELL_ENABLED } from 'config';
import clearFormDataFromSessionStorage from 'lib/checkout/clearFormDataFromSessionStorage';
import MobileAppCloseRefreshIcon from 'components/MobileAppCloseRefreshIcon';
import { clearBooking } from 'store/booking/bookingActions';
import { fetchFaqs } from 'store/faqs/faqActions';
import { useIsAuthenticated, useLogout } from 'lib/oauth';
import TravelInsuranceCrossSellBanner from './TravelInsuranceCrossSellBanner';
import FeatureCard from 'components/FeatureCard';
import { useFeature } from '@optimizely/react-sdk';

const CenteredWrapper = styled(Box)`
  margin: 0 auto;
`;

const DynamicIcon = styled(Icon)`
  height: 24px;
  width: 24px;
  ${mediaQuery.minWidth.sm} {
    height: 32px;
    width: 32px;
  }
`;

const PrintOnlySection = ({ children }) => (
  <Hide xs sm md lg>
    {children}
  </Hide>
);

PrintOnlySection.propTypes = { children: PropTypes.node.isRequired };

const Section = ({ children, ...rest }) => (
  <Box py={8} borderBottom={1} borderColor="greys.alto" {...rest} data-print-style="section">
    {children}
  </Box>
);

Section.propTypes = { children: PropTypes.node.isRequired };

const BookingConfirmationLayout = ({ pageTitle }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const knowBeforeYouGoRef = useRef();
  const loadingError = useSelector(getLoadingError);
  const bookingReference = useSelector(getBookingReference);
  const property = useSelector(getProperty);
  const country = get(property, 'address.country');
  const isLoading = useSelector(getIsLoading);
  const isAuthenticated = useIsAuthenticated();

  const { bookingid: bookingId } = router.query;
  const { logout } = useLogout();
  const hasPropertyRoomInfo = !!get(property, 'roomInformation');
  const isBookingLoaded = !!bookingReference;
  const [isActivitiesEnabled] = useFeature('qantas-activities-customer');

  useMount(() => {
    dispatch(fetchFaqs());

    if (QFF_ACCOUNT_MANAGEMENT === ACCOUNT_MANAGEMENT_TYPES.CHECKOUT_ONLY && isAuthenticated) {
      logout();
    }

    clearFormDataFromSessionStorage();
  });

  useUnmount(() => {
    dispatch(clearBooking());
  });

  return (
    <>
      <Head>
        <title>{pageTitle}</title>
        {BOOKING_CONFIRMATION_PAGE_META.map(({ name, content }) => (
          <meta key={name} name={name} content={content} />
        ))}
      </Head>
      <MobileAppCloseRefreshIcon />
      {isLoading && (
        <PageBlock mt={10}>
          <LoadingIndicator />
        </PageBlock>
      )}
      {loadingError && <LoadingError error={loadingError} />}
      {isBookingLoaded && (
        <Fragment>
          <PageBlock bg="porcelain" borderBottom={1} borderColor="greys.porcelain" py={10} px={[4, 6, 0]} data-print-style="page-block">
            <CenteredWrapper width={[1, 1, 0.75]}>
              <Flex alignItems="center" mb={4} data-print-style="confirmation-header">
                <DynamicIcon name="checkCircle" color="darkGreen" />
                <Heading.h1 data-testid="confirmation-header" fontSize={['base', 'xl']} fontWeight={['bold', 'normal']} ml={[2, 4]} mb={0}>
                  Your booking has been confirmed.
                </Heading.h1>
              </Flex>
              <BookingSummary knowBeforeYouGoRef={knowBeforeYouGoRef} bookingId={bookingId} />

              {isActivitiesEnabled && <FeatureCard country={country} />}
            </CenteredWrapper>
          </PageBlock>
          <PrintOnlySection>
            <PageBlock bg="white" borderColor="greys.porcelain" py={10} data-print-style="page-block">
              <CenteredWrapper width={[1, 1, 0.75]}>
                <QHContactDetails />
              </CenteredWrapper>
            </PageBlock>
          </PrintOnlySection>
          <PageBlock bg="white" borderColor="greys.porcelain" py={10} px={[4, 6, 0]} data-print-style="page-block">
            <CenteredWrapper width={[1, 1, 0.75]}>
              {TRAVEL_INSURANCE_CROSS_SELL_ENABLED && <TravelInsuranceCrossSellBanner />}
              <Heading.h2 mb={8} fontSize={['md', 'lg']} data-print-style="h2">
                Your booking details
              </Heading.h2>
              <Section paddingTop={0}>
                <HotelInformation property={property} />
              </Section>
              <Section>
                <ReservationDates />
              </Section>
              <Section>
                <GuestDetails />
              </Section>
              <Section>
                <RoomDetails />
              </Section>
              <Section pt={8}>
                <PaymentDetails />
              </Section>
            </CenteredWrapper>
          </PageBlock>
          <PageBlock bg="white" borderColor="greys.porcelain" py={10} px={[4, 6, 0]} ref={knowBeforeYouGoRef} data-print-style="page-block">
            <CenteredWrapper width={[1, 1, 0.75]}>
              <Heading.h2 fontSize={['md', 'lg']} data-print-style="h2">
                Important information to know before you go
              </Heading.h2>
              <Section>
                <CancellationPolicy />
              </Section>
              <Section>
                <CheckInInformation />
              </Section>
              {hasPropertyRoomInfo && (
                <Section>
                  <PropertyPolicies />
                </Section>
              )}
              <Box pt={8} data-print-style="section">
                <PropertyDescription />
              </Box>
            </CenteredWrapper>
          </PageBlock>
          <PageBlock bg="white" borderColor="white" pt={10} pb={20} px={[4, 6, 0]} display={['none', 'block']}>
            <CenteredWrapper width={[1, 1, 0.75]}>
              <BookingConfirmationContactUsLinks />
            </CenteredWrapper>
          </PageBlock>
          <PageBlock bg="white" borderColor="greys.porcelain" pt={10} pb={20} px={[4, 6, 0]} display={['none', 'block']}>
            <CenteredWrapper width={[1, 1, 0.75]}>
              <BookingConfirmationFaqsLinks />
            </CenteredWrapper>
          </PageBlock>
        </Fragment>
      )}
      <PrintOnlySection>
        <PageBlock bg="white" borderColor="greys.porcelain" py={10} data-print-style="page-block">
          <CenteredWrapper width={[1, 1, 0.75]}>
            <ImportantInformationFooter />
          </CenteredWrapper>
        </PageBlock>
      </PrintOnlySection>
    </>
  );
};

BookingConfirmationLayout.propTypes = {
  pageTitle: PropTypes.string,
};

BookingConfirmationLayout.defaultProps = {
  pageTitle: `Booking Confirmation | ${HOTELS_BRAND_NAME} Australia`,
};

BookingConfirmationLayout.displayName = 'BookingConfirmationLayout';

export default BookingConfirmationLayout;
