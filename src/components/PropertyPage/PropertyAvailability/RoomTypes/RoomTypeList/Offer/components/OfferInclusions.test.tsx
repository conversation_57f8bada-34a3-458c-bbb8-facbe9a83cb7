import React from 'react';
import { ReactWrapper } from 'enzyme';

import OfferInclusions from './OfferInclusions';
import { mocked, mountUtils } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';
import { inclusionsData, valueAdds } from '../fixtures';

jest.mock('store/ui/uiSelectors');
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

const mockOnClick = jest.fn();

const defaultProps = {
  onClick: mockOnClick,
};

const renderComponent = (props = {}) =>
  mountUtils(<OfferInclusions {...props} {...defaultProps} />, { decorators: { theme: true, store: true } });

const checkInclusionsDisplayed = (wrapper, expectedInclusions) => {
  const inclusionsPreview = wrapper.findByTestId('inclusions-preview-wrapper').children();
  expect(inclusionsPreview).toHaveLength(expectedInclusions.length);
  expectedInclusions.forEach((inclusion, index) => {
    expect(inclusionsPreview.at(index)).toHaveText(inclusion);
  });
};

const checkIconsDisplayed = (wrapper: ReactWrapper, expectedIcons: string[] = [], notExpectedIcons: string[] = []) => {
  const icons = wrapper.find('Icon');
  expectedIcons.forEach((iconName) => {
    expect(icons.filter({ name: iconName })).toExist();
  });
  notExpectedIcons.forEach((iconName) => {
    expect(icons.filter({ name: iconName })).not.toExist();
  });
};

const checkViewAllButton = (wrapper: ReactWrapper, shouldExist: boolean, text: string | null = null) => {
  const viewAllButton = wrapper.findByTestId('view-all-inclusions');
  if (shouldExist) {
    expect(viewAllButton).toExist();
    if (text) {
      expect(viewAllButton).toHaveText(text);
    }
  } else {
    expect(viewAllButton).not.toExist();
  }
};

describe('with less than 3 inclusions', () => {
  describe('and no valueAdds', () => {
    describe('InclusionsPreviewWrapper', () => {
      describe('when useGlobalInclusions is true', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([true]);
        });

        it('displays a list of all inclusions in order', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkInclusionsDisplayed(wrapper, ['Guaranteed Room Upgrade', 'All Inclusive']);
        });

        it('displays an icon for mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkIconsDisplayed(wrapper, ['incQfUpgrade', 'incQfAllinclusive']);
        });

        it('does not display the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkViewAllButton(wrapper, false);
        });
      });

      describe('when useGlobalInclusions is false', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([false]);
        });

        it('displays a list of all inclusions in order', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkInclusionsDisplayed(wrapper, ['All Inclusive', 'Guaranteed Room Upgrade']);
        });

        it('displays an icon for mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkIconsDisplayed(wrapper, ['arrowUpward', 'allInclusive']);
        });

        it('does not display the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree });
          checkViewAllButton(wrapper, false);
        });
      });
    });
  });

  describe('and with valueAdds', () => {
    describe('InclusionsPreviewWrapper', () => {
      describe('when useGlobalInclusions is true', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([true]);
        });

        it('displays all inclusions with the valueAdds first', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree, valueAdds });
          checkInclusionsDisplayed(wrapper, ['Dinner for two', 'Guaranteed Room Upgrade', 'All Inclusive']);
        });
      });

      describe('when useGlobalInclusions is false', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([false]);
        });

        it('displays all inclusions with the valueAdds first', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.lessThanThree, valueAdds });
          checkInclusionsDisplayed(wrapper, ['Dinner for two', 'All Inclusive', 'Guaranteed Room Upgrade']);
        });
      });
    });
  });
});

describe('with more than 3 inclusions', () => {
  describe('and no valueAdds', () => {
    describe('InclusionsPreviewWrapper', () => {
      describe('when useGlobalInclusions is true', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([true]);
        });

        it('displays only the first 3 inclusions as for the ORDERED_INCLUSION_WHITELIST', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkInclusionsDisplayed(wrapper, ['Offer Inclusions', 'Breakfast Included', 'All Inclusive']);
        });

        it('displays an icon for the 3 mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkIconsDisplayed(
            wrapper,
            ['incQfGeneric', 'incQfBreakfast', 'incQfAllinclusive'],
            ['incQfWifi', 'incQfCheckin', 'incQfCheckout', 'incQfMeal'],
          );
        });

        it('displays the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkViewAllButton(wrapper, true, '+ 4 more inclusions');
        });

        it('displays the correct inclusion spelling if there is only four inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.onlyFour });
          checkViewAllButton(wrapper, true, '+ 1 more inclusion');
        });
      });

      describe('when useGlobalInclusions is false', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([false]);
        });

        it('displays only the first 3 inclusions as for the ORDERED_INCLUSION_WHITELIST', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkInclusionsDisplayed(wrapper, ['All Inclusive', 'Breakfast Included', 'Dinner Included']);
        });

        it('displays an icon for the 3 mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkIconsDisplayed(wrapper, ['allInclusive', 'freeBreakfast', 'restaurant'], ['accessTime', 'earlyCheckin', 'wifi']);
        });

        it('displays the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree });
          checkViewAllButton(wrapper, true, '+ 4 more inclusions');
        });
      });
    });

    it('shows the InclusionsList with all the inclusions when the view all button is clicked', () => {
      const { findByTestId } = renderComponent({ inclusions: inclusionsData.moreThanThree });
      const viewAllButton = findByTestId('view-all-inclusions');

      viewAllButton.simulate('click');

      expect(findByTestId('inclusions-preview-wrapper')).toBeDefined();
    });
  });

  describe('and valueAdds', () => {
    describe('InclusionsPreviewWrapper', () => {
      describe('when useGlobalInclusions is true', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([true]);
        });

        it('displays only the first 3 valueAdds plus inclusions with ValuesAdds on top', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkInclusionsDisplayed(wrapper, ['Dinner for two', 'Offer Inclusions', 'Breakfast Included']);
        });

        it('displays ValueAdds icon and the 2 top icon for the mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkIconsDisplayed(
            wrapper,
            ['welcomeGift', 'incQfGeneric', 'incQfBreakfast'],
            ['incQfMeal', 'incQfCheckout', 'incQfCheckin', 'incQfWifi'],
          );
        });

        it('displays the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkViewAllButton(wrapper, true, '+ 5 more inclusions');
        });
      });

      describe('when useGlobalInclusions is false', () => {
        beforeEach(() => {
          mocked(useFeature).mockReturnValue([false]);
        });

        it('displays only the first 3 valueAdds plus inclusions with ValuesAdds on top', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkInclusionsDisplayed(wrapper, ['Dinner for two', 'All Inclusive', 'Breakfast Included']);
        });

        it('displays ValueAdds icon and the 2 top icon for the mapped inclusions', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkIconsDisplayed(
            wrapper,
            ['welcomeGift', 'allInclusive', 'freeBreakfast'],
            ['restaurant', 'accessTime', 'earlyCheckin', 'wifi'],
          );
        });

        it('displays the view all message', () => {
          const wrapper = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
          checkViewAllButton(wrapper, true, '+ 5 more inclusions');
        });
      });
    });

    it('shows the InclusionsList with all the inclusions when the view all button is clicked', () => {
      const { findByTestId } = renderComponent({ inclusions: inclusionsData.moreThanThree, valueAdds });
      const viewAllButton = findByTestId('view-all-inclusions');

      viewAllButton.simulate('click');

      expect(findByTestId('inclusions-preview-wrapper')).toBeDefined();
    });
  });
});

it('can handle empty array of inclusions', () => {
  const { find } = renderComponent({ inclusions: [] });
  const inclusionIcons = find('Icon');
  expect(inclusionIcons).toHaveLength(0);
});

it('can handle null inclusions', () => {
  const { find } = renderComponent();
  const inclusionIcons = find('Icon');
  expect(inclusionIcons).toHaveLength(0);
});
