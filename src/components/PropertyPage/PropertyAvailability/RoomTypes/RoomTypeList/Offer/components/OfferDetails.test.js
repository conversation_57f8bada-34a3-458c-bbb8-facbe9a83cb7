import React from 'react';
import OfferDetails from './OfferDetails';
import { mountUtils, mocked } from 'test-utils';
import { useFeature } from '@optimizely/react-sdk';

jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));

mountUtils.mockComponent('OfferDetailsModal');
mountUtils.mockComponent('OfferDetailsTooltip');
mountUtils.mockComponent('OfferValueAdds');

const offerWithValueAdds = {
  name: 'A great offer',
  description: 'Standard Twin Room',
  inclusions: [
    { code: 'internet', description: 'Free wifi', icon: 'incQfWifi' },
    { code: 'dinner', description: 'One free meal', icon: 'incQfMeal' },
  ],
  valueAdds: ['Free breakfast buffet for 2 for day'],
};

const offerWithNoValueAdds = { ...offerWithValueAdds, valueAdds: [] };

const defaultProps = {
  counter: 1,
  offer: offerWithValueAdds,
};

const decorators = { theme: true, store: true };
const render = (props = {}) => mountUtils(<OfferDetails {...defaultProps} {...props} />, { decorators });

beforeEach(() => {
  jest.resetAllMocks();
  mocked(useFeature).mockReturnValue([true]);
});

describe('The Offer details', () => {
  it('displays the offer name', () => {
    const { findByText } = render();
    expect(findByText(defaultProps.offer.name)).toExist();
  });

  describe('Inclusions', () => {
    describe('when useGlobalInclusions is true', () => {
      beforeEach(() => {
        mocked(useFeature).mockReturnValue([true]);
      });

      describe('with valuesAdds', () => {
        it('renders the inclusions and valuesAdd', () => {
          const { find } = render();
          expect(find('Flag')).toHaveLength(3);
        });

        it('renders first the valuesAdd', () => {
          const { find } = render();
          expect(find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
        });
      });

      describe('with NO valuesAdds', () => {
        it('renders only the inclusions', () => {
          const { find } = render({ ...defaultProps, offer: offerWithNoValueAdds });
          expect(find('Flag')).toHaveLength(2);
        });

        it('renders the ordered inclusions', () => {
          const { find } = render({ ...defaultProps, offer: offerWithNoValueAdds });
          expect(find('Flag').at(0)).toHaveProp({ icon: 'incQfWifi' });
        });
      });
    });

    describe('when useGlobalInclusions is false', () => {
      beforeEach(() => {
        mocked(useFeature).mockReturnValue([false]);
      });

      describe('with valuesAdds', () => {
        it('renders the inclusions and valuesAdd', () => {
          const { find } = render();
          expect(find('Flag')).toHaveLength(3);
        });

        it('renders first the valuesAdd', () => {
          const { find } = render();
          expect(find('Flag').at(0)).toHaveProp({ icon: 'welcomeGift' });
        });
      });

      describe('with NO valuesAdds', () => {
        it('renders only the inclusions', () => {
          const { find } = render({ ...defaultProps, offer: offerWithNoValueAdds });
          expect(find('Flag')).toHaveLength(2);
        });

        it('renders the ordered inclusions', () => {
          const { find } = render({ ...defaultProps, offer: offerWithNoValueAdds });
          expect(find('Flag').at(0)).toHaveProp({ icon: 'restaurant' });
        });
      });
    });
  });

  describe('The Show Details link', () => {
    it('displays the OfferDetailsModal component', () => {
      const { find } = render();
      expect(find('OfferDetailsModal').first()).toHaveProp({
        offerDescription: defaultProps.offer.description,
        offerName: defaultProps.offer.name,
      });
    });
  });
});
