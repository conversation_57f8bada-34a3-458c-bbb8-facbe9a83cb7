import React from 'react';
import PropTypes from 'prop-types';
import compact from 'lodash/compact';
import take from 'lodash/take';
import { Flex, Box, Icon } from '@qga/roo-ui/components';
import Tooltip from 'components/Tooltip';
import { useFeature } from '@optimizely/react-sdk';

const ORDERED_INCLUSION_WHITELIST = [
  { code: 'internet', icon: 'wifi' },
  { code: 'breakfast', icon: 'restaurant' },
  { code: 'parking', icon: 'directionsCar' },
];

const Inclusions = ({ inclusions: allInclusions, ...rest }) => {
  const [isGlobalInclusionsEnabled] = useFeature('qantas_hotels_global_inclusions');
  const displayInclusions = isGlobalInclusionsEnabled
    ? allInclusions
    : compact(
        ORDERED_INCLUSION_WHITELIST.map((inclusionWhitelist) => {
          const inclusion = allInclusions.find((inclusion) => inclusion.code === inclusionWhitelist.code);
          return inclusion ? { ...inclusion, icon: inclusionWhitelist.icon } : null;
        }),
      );

  const maxDisplayInclusions = take(displayInclusions, 3);

  return (
    <Flex {...rest}>
      {maxDisplayInclusions.map((inclusion, index) => (
        <Box key={index} data-testid="inclusion-old" title={inclusion.description}>
          <Tooltip label={inclusion.description}>
            <Flex p={3} justifyContent="center" alignItems="center" borderRadius="rounded" mr={2}>
              <Icon name={inclusion.icon} color="ui.iconBadge" size={16} />
            </Flex>
          </Tooltip>
        </Box>
      ))}
    </Flex>
  );
};

Inclusions.propTypes = {
  inclusions: PropTypes.array.isRequired,
};

export default Inclusions;
