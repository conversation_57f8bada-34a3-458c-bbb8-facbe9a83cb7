import React from 'react';
import OfferCheckoutLink from './OfferCheckoutLink';
import { mountUtils } from 'test-utils';
import { getFullKnownQuery } from 'store/router/routerSelectors';
import { getAllAvailableOffers } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getRoomTypesWithoutOffers, getProperty } from 'store/property/propertySelectors';
import { useDataLayer } from 'hooks/useDataLayer';
import { useRouter } from 'next/router';
import AppLink from 'components/AppLink';
import { getAvailableRoomTypes } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { GetShowCtaMessage } from 'lib/analytics/eventsMap/helpers/GetShowCtaMessage';
import useCtaClickEvent from 'hooks/useCtaClickEvent';

jest.mock('store/router/routerSelectors');
jest.mock('store/exclusiveOffer/exclusiveOfferSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/propertyAvailability/propertyAvailabilitySelectors');
jest.mock('store/pointsBurnTiers/pointsBurnSelectors');
jest.mock('hooks/useDataLayer');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@optimizely/react-sdk', () => ({
  useFeature: jest.fn(),
}));
jest.mock('lib/analytics/eventsMap/helpers/GetShowCtaMessage', () => ({
  GetShowCtaMessage: jest.fn(),
}));
jest.mock('hooks/useCtaClickEvent', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    ctaClickEvent: jest.fn(),
  })),
}));

const defaultProps = {
  children: 'Book now',
  propertyId: '1',
  roomTypeId: '2',
  offerId: '3',
  offerName: 'Best Rate',
  roomName: 'Standard King',
};

const query = {
  checkIn: new Date(2020, 10, 10),
  checkOut: new Date(2020, 10, 11),
  payWith: 'cash',
  exclusiveOffer: false,
};

const mockRouter = {
  query,
};

const mockAvailableOffers = {
  3: {
    allocationsAvailable: 1,
    id: '3',
    charges: {
      payableAtBooking: { total: { amount: 1553, currency: 'PTS' } },
    },
  },
};

const mockRoomTypes = [
  {
    id: '2',
    name: 'Standard Double Room',
    mainImage: {
      caption: 'Room',
      urlMedium: 'https://i.travelapi.com/lodging/37000000/36840000/36833100/36833039/24c34abe_b.jpg',
    },
  },
];

const mockProperty = {
  id: '1234',
  name: 'Mock Hotel',
  category: 'hotels',
  rating: 4,
  address: {
    state: 'New South Wales',
    country: 'Australia',
  },
};

const mockCtaClickEvent = jest.fn();

const emitInteractionEvent = jest.fn();

const render = (props) => mountUtils(<OfferCheckoutLink {...defaultProps} {...props} />, { decorators: { store: true, router: true } });

beforeEach(() => {
  jest.clearAllMocks();
  getFullKnownQuery.mockReturnValue(query);
  getAllAvailableOffers.mockReturnValue(mockAvailableOffers);
  getRoomTypesWithoutOffers.mockReturnValue(mockRoomTypes);
  getAvailableRoomTypes.mockReturnValue(mockRoomTypes);
  getProperty.mockReturnValue(mockProperty);
  useDataLayer.mockReturnValue({ emitInteractionEvent });
  useRouter.mockReturnValue(mockRouter);
  GetShowCtaMessage.mockReturnValue(false);
  useCtaClickEvent.mockReturnValue({ ctaClickEvent: mockCtaClickEvent });
});

describe('when clicking the link', () => {
  it('sends an event to the data layer', () => {
    const { find } = render();

    find('ButtonLink').simulate('click');

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Select Offer Button',
      value: 'Best Rate',
    });
  });

  it('emits the ctaClickEvent', () => {
    const { find } = render();

    find('ButtonLink').simulate('click');

    expect(mockCtaClickEvent).toHaveBeenCalledWith({
      itemText: 'Book now',
      itemType: 'button',
      url: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
    });
  });

  describe('with showCtaMessage false', () => {
    it('dispatches addToCart with the correct parameters', () => {
      const { find, decorators } = render();

      find('ButtonLink').simulate('click');

      expect(decorators.store.dispatch).toHaveBeenCalledWith({
        type: 'checkout/ADD_TO_CART',
        payload: {
          ctaMessage: '',
          ctaMessageCategory: '',
          initialCash: null,
          isRebooked: false,
          offer: mockAvailableOffers[3],
          pointsConversion: undefined,
          property: mockProperty,
          query,
          roomType: mockRoomTypes[0],
        },
      });
    });
  });

  describe('with showCtaMessage true', () => {
    beforeEach(() => {
      GetShowCtaMessage.mockReturnValue(true);
    });

    it('dispatches addToCart with the correct parameters', () => {
      const { find, decorators } = render();

      find('ButtonLink').simulate('click');

      expect(decorators.store.dispatch).toHaveBeenCalledWith({
        type: 'checkout/ADD_TO_CART',
        payload: {
          ctaMessage: 'Hurry, we only have 1 room left!',
          ctaMessageCategory: 'available rooms',
          initialCash: null,
          isRebooked: false,
          offer: mockAvailableOffers[3],
          pointsConversion: undefined,
          property: mockProperty,
          query,
          roomType: mockRoomTypes[0],
        },
      });
    });
  });
});

describe('without initialCash', () => {
  it('renders the href without initialCash', () => {
    const { find } = render();
    expect(find('ButtonLink')).toHaveProp({
      as: AppLink,
      variant: 'primary',
      to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
    });
  });
});

describe('without payWith', () => {
  it('renders the href with payWith from the query', () => {
    const { find } = render();
    expect(find('ButtonLink')).toHaveProp({
      as: AppLink,
      variant: 'primary',
      to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
    });
  });
});

describe('initialCash', () => {
  describe('with initialCash', () => {
    it('renders the href with initialCash', () => {
      getFullKnownQuery.mockReturnValue({
        checkIn: new Date(2020, 10, 10),
        checkOut: new Date(2020, 10, 11),
        payWith: 'points',
      });

      const { find } = render({ initialCash: 100 });
      expect(find('ButtonLink')).toHaveProp({
        as: AppLink,
        variant: 'primary',
        to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&initialCash=100&offerId=3&payWith=points&propertyId=1&roomTypeId=2',
      });
    });

    describe('with payWith of cash', () => {
      it('renders the href with payWith of cash', () => {
        const { find } = render({ initialCash: 100 });
        expect(find('ButtonLink')).toHaveProp({
          as: AppLink,
          variant: 'primary',
          to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&initialCash=100&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
        });
      });
    });
  });

  describe('with payWith of points but no initialCash', () => {
    it('renders the href with initialCash=0', () => {
      getFullKnownQuery.mockReturnValue({
        checkIn: new Date(2020, 10, 10),
        checkOut: new Date(2020, 10, 11),
        payWith: 'points',
      });
      const { find } = render();

      expect(find('ButtonLink')).toHaveProp({
        as: AppLink,
        variant: 'primary',
        to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=points&propertyId=1&roomTypeId=2',
      });
    });
  });

  describe('with payWith of cash', () => {
    it('renders the href without initialCash', () => {
      const { find } = render();

      expect(find('ButtonLink')).toHaveProp({
        as: AppLink,
        variant: 'primary',
        to: '/checkout?checkIn=2020-11-10&checkOut=2020-11-11&exclusiveOffer=false&offerId=3&payWith=cash&propertyId=1&roomTypeId=2',
      });
    });
  });
});
